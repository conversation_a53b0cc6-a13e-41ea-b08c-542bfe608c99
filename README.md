# QR Code Generator

ระบบสร้าง QR Code ที่สร้างด้วย Next.js, TypeScript และ Tailwind CSS

## คุณสมบัติ

- 🎯 **สร้าง QR Code ได้ง่าย** - เพียงใส่ข้อความหรือ URL
- 🎨 **ปรับแต่งได้** - เปลี่ยนสี, ขนาด, และระดับการแก้ไขข้อผิดพลาด
- 📱 **Responsive Design** - ใช้งานได้ทั้งบนมือถือและคอมพิวเตอร์
- 💾 **ดาวน์โหลดได้** - บันทึก QR Code เป็นไฟล์ PNG
- 📋 **คัดลอกได้** - คัดลอก QR Code ไปยังคลิปบอร์ด
- ⚡ **รวดเร็ว** - สร้าง QR Code แบบ Real-time

## เทคโนโลยีที่ใช้

- **Next.js 15** - React Framework
- **TypeScript** - Type Safety
- **Tailwind CSS** - Styling
- **QRCode.js** - QR Code Generation
- **React Hooks** - State Management

## การติดตั้งและรัน

1. **Clone โปรเจค**
   ```bash
   git clone <repository-url>
   cd QRcode
   ```

2. **ติดตั้ง Dependencies**
   ```bash
   npm install
   ```

3. **รันเซิร์ฟเวอร์ Development**
   ```bash
   npm run dev
   ```

4. **เปิดเบราว์เซอร์**
   ไปที่ [http://localhost:3000](http://localhost:3000)

## การใช้งาน

### การสร้าง QR Code พื้นฐาน
1. ใส่ข้อความหรือ URL ในช่องข้อความ
2. กดปุ่ม "สร้าง QR Code"
3. QR Code จะแสดงขึ้นทางด้านขวา

### ตัวเลือกขั้นสูง
คลิก "ตัวเลือกขั้นสูง" เพื่อปรับแต่ง:
- **ระดับการแก้ไขข้อผิดพลาด**: L (ต่ำ), M (ปานกลาง), Q (สูง), H (สูงมาก)
- **ขนาด**: 128-1024 พิกเซล
- **สีเข้ม/สีอ่อน**: เลือกสีที่ต้องการ

### การดาวน์โหลดและคัดลอก
- **ดาวน์โหลด**: กดปุ่ม "ดาวน์โหลด" เพื่อบันทึกเป็นไฟล์ PNG
- **คัดลอก**: กดปุ่ม "คัดลอก" เพื่อคัดลอกไปยังคลิปบอร์ด

## โครงสร้างโปรเจค

```
src/
├── app/
│   ├── globals.css      # Global styles
│   ├── layout.tsx       # Root layout
│   └── page.tsx         # Home page
└── components/
    └── QRCodeGenerator.tsx  # QR Code generator component
```

## Scripts

- `npm run dev` - รันเซิร์ฟเวอร์ development
- `npm run build` - สร้าง production build
- `npm run start` - รัน production server
- `npm run lint` - ตรวจสอบ code style

## การ Deploy

### Vercel (แนะนำ)
1. Push โค้ดไปยัง GitHub
2. เชื่อมต่อ repository กับ Vercel
3. Deploy อัตโนมัติ

### การ Deploy อื่นๆ
```bash
npm run build
npm run start
```

## การพัฒนาเพิ่มเติม

### เพิ่มฟีเจอร์ใหม่
- สร้าง QR Code แบบ Batch
- เพิ่มรูปแบบไฟล์อื่นๆ (SVG, PDF)
- เพิ่ม Logo ใน QR Code
- ประวัติการสร้าง QR Code

### การปรับปรุง UI/UX
- Dark Mode
- Animation เมื่อสร้าง QR Code
- Preview แบบ Real-time
- Drag & Drop สำหรับไฟล์

## License

MIT License - ใช้งานได้อย่างอิสระ

## ผู้พัฒนา

สร้างด้วย ❤️ โดยใช้ Next.js และ TypeScript
