(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{883:(e,r,t)=>{Promise.resolve().then(t.bind(t,3022))},3022:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(5155),l=t(2115),a=t(6862),o=t(6766);function n(){let[e,r]=(0,l.useState)(""),[t,n]=(0,l.useState)(""),[i,d]=(0,l.useState)(!1),[c,x]=(0,l.useState)({errorCorrectionLevel:"M",type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"},width:256}),[m,u]=(0,l.useState)(!1),h=(0,l.useRef)(null),g=async()=>{if(e.trim()){d(!0);try{let r=await a.toDataURL(e,c);n(r),h.current&&await a.toCanvas(h.current,e,c)}catch(e){console.error("Error generating QR code:",e),alert("เกิดข้อผิดพลาดในการสร้าง QR Code")}finally{d(!1)}}},p=async()=>{if(t)try{let e=await fetch(t),r=await e.blob();await navigator.clipboard.write([new ClipboardItem({"image/png":r})]),alert("คัดลอก QR Code ไปยังคลิปบอร์ดแล้ว!")}catch(e){console.error("Error copying to clipboard:",e),alert("ไม่สามารถคัดลอกได้ กรุณาใช้ปุ่มดาวน์โหลดแทน")}};return(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"text-input",className:"block text-sm font-medium text-gray-700 mb-2",children:"ข้อความหรือ URL"}),(0,s.jsx)("textarea",{id:"text-input",value:e,onChange:e=>r(e.target.value),placeholder:"ใส่ข้อความ, URL, หรือข้อมูลที่ต้องการสร้าง QR Code...",className:"w-full h-32 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",maxLength:2e3}),(0,s.jsxs)("div",{className:"text-right text-sm text-gray-500 mt-1",children:[e.length,"/2000"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("button",{onClick:()=>u(!m),className:"flex items-center text-sm font-medium text-blue-600 hover:text-blue-800",children:[(0,s.jsx)("span",{children:"ตัวเลือกขั้นสูง"}),(0,s.jsx)("svg",{className:"ml-1 h-4 w-4 transform transition-transform ".concat(m?"rotate-180":""),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),m&&(0,s.jsxs)("div",{className:"mt-4 space-y-4 p-4 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ระดับการแก้ไขข้อผิดพลาด"}),(0,s.jsxs)("select",{value:c.errorCorrectionLevel,onChange:e=>x({...c,errorCorrectionLevel:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,s.jsx)("option",{value:"L",children:"ต่ำ (L)"}),(0,s.jsx)("option",{value:"M",children:"ปานกลาง (M)"}),(0,s.jsx)("option",{value:"Q",children:"สูง (Q)"}),(0,s.jsx)("option",{value:"H",children:"สูงมาก (H)"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ขนาด (px)"}),(0,s.jsx)("input",{type:"number",value:c.width,onChange:e=>x({...c,width:parseInt(e.target.value)||256}),min:"128",max:"1024",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"สีเข้ม"}),(0,s.jsx)("input",{type:"color",value:c.color.dark,onChange:e=>x({...c,color:{...c.color,dark:e.target.value}}),className:"w-full h-10 border border-gray-300 rounded-md"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"สีอ่อน"}),(0,s.jsx)("input",{type:"color",value:c.color.light,onChange:e=>x({...c,color:{...c.color,light:e.target.value}}),className:"w-full h-10 border border-gray-300 rounded-md"})]})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,s.jsx)("button",{onClick:g,disabled:!e.trim()||i,className:"flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:i?"กำลังสร้าง...":"สร้าง QR Code"}),(0,s.jsx)("button",{onClick:()=>{r(""),n("")},className:"flex-1 bg-gray-500 text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-600 transition-colors",children:"ล้างข้อมูล"})]})]}),(0,s.jsx)("div",{className:"flex flex-col items-center justify-center space-y-6",children:t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"bg-white p-4 rounded-lg shadow-md",children:(0,s.jsx)(o.default,{src:t,alt:"Generated QR Code",width:c.width,height:c.width,className:"max-w-full h-auto"})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full",children:[(0,s.jsxs)("button",{onClick:()=>{if(!t)return;let e=document.createElement("a");e.download="qrcode-".concat(Date.now(),".png"),e.href=t,e.click()},className:"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"ดาวน์โหลด"]}),(0,s.jsxs)("button",{onClick:p,className:"flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})}),"คัดลอก"]})]})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-64 text-gray-400",children:[(0,s.jsx)("svg",{className:"w-16 h-16 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"QR Code จะแสดงที่นี่"}),(0,s.jsx)("p",{className:"text-sm",children:"ใส่ข้อความแล้วกดสร้าง QR Code"})]})})]}),(0,s.jsx)("canvas",{ref:h,style:{display:"none"}})]})}function i(){return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"QR Code Generator"}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:"สร้าง QR Code ได้ง่ายๆ เพียงใส่ข้อความหรือ URL ที่ต้องการ"})]}),(0,s.jsx)(n,{})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[355,441,684,358],()=>r(883)),_N_E=e.O()}]);