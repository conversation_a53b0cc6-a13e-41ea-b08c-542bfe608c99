"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/QRCodeGenerator.tsx":
/*!********************************************!*\
  !*** ./src/components/QRCodeGenerator.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ QRCodeGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var qrcode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! qrcode */ \"(app-pages-browser)/./node_modules/qrcode/lib/browser.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction QRCodeGenerator() {\n    _s();\n    const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [qrCodeUrl, setQrCodeUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [options, setOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        errorCorrectionLevel: 'M',\n        type: 'image/png',\n        quality: 0.92,\n        margin: 1,\n        color: {\n            dark: '#000000',\n            light: '#FFFFFF'\n        },\n        width: 256\n    });\n    const [showAdvanced, setShowAdvanced] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const generateQRCode = async ()=>{\n        if (!text.trim()) return;\n        setIsGenerating(true);\n        try {\n            const url = await qrcode__WEBPACK_IMPORTED_MODULE_2__.toDataURL(text, options);\n            setQrCodeUrl(url);\n            // Also generate to canvas for download functionality\n            if (canvasRef.current) {\n                await qrcode__WEBPACK_IMPORTED_MODULE_2__.toCanvas(canvasRef.current, text, options);\n            }\n        } catch (error) {\n            console.error('Error generating QR code:', error);\n            alert('เกิดข้อผิดพลาดในการสร้าง QR Code');\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const downloadQRCode = ()=>{\n        if (!qrCodeUrl) return;\n        const link = document.createElement('a');\n        link.download = \"qrcode-\".concat(Date.now(), \".png\");\n        link.href = qrCodeUrl;\n        link.click();\n    };\n    const copyToClipboard = async ()=>{\n        if (!qrCodeUrl) return;\n        try {\n            const response = await fetch(qrCodeUrl);\n            const blob = await response.blob();\n            await navigator.clipboard.write([\n                new ClipboardItem({\n                    'image/png': blob\n                })\n            ]);\n            alert('คัดลอก QR Code ไปยังคลิปบอร์ดแล้ว!');\n        } catch (error) {\n            console.error('Error copying to clipboard:', error);\n            alert('ไม่สามารถคัดลอกได้ กรุณาใช้ปุ่มดาวน์โหลดแทน');\n        }\n    };\n    const clearAll = ()=>{\n        setText('');\n        setQrCodeUrl('');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg p-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"text-input\",\n                                        className: \"block text-lg font-bold text-gray-500 mb-2\",\n                                        children: \"ข้อความหรือ URL\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"text-input\",\n                                        value: text,\n                                        onChange: (e)=>setText(e.target.value),\n                                        placeholder: \"ใส่ข้อความ, URL, หรือข้อมูลที่ต้องการสร้าง QR Code...\",\n                                        className: \"w-full h-32 px-4 py-3 outline-none text-gray-400 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\",\n                                        maxLength: 2000\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right text-sm text-gray-500 mt-1\",\n                                        children: [\n                                            text.length,\n                                            \"/2000\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAdvanced(!showAdvanced),\n                                        className: \"flex items-center text-md font-medium text-gray-500 hover:text-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"ตัวเลือกขั้นสูง\"\n                                            }, void 0, false, {\n                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"ml-1 h-4 w-4 transform transition-transform \".concat(showAdvanced ? 'rotate-180' : ''),\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 9l-7 7-7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    showAdvanced && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 space-y-4 p-4 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-500 mb-1\",\n                                                                children: \"ระดับการแก้ไขข้อผิดพลาด\"\n                                                            }, void 0, false, {\n                                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: options.errorCorrectionLevel,\n                                                                onChange: (e)=>setOptions({\n                                                                        ...options,\n                                                                        errorCorrectionLevel: e.target.value\n                                                                    }),\n                                                                className: \"w-full px-3 py-1.25 border text-sm text-gray-500 border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"L\",\n                                                                        children: \"ต่ำ (L)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                        lineNumber: 138,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"M\",\n                                                                        children: \"ปานกลาง (M)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                        lineNumber: 139,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"Q\",\n                                                                        children: \"สูง (Q)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                        lineNumber: 140,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"H\",\n                                                                        children: \"สูงมาก (H)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                        lineNumber: 141,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                lineNumber: 133,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-500 mb-1\",\n                                                                children: \"ขนาด (px)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                lineNumber: 146,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"number\",\n                                                                value: options.width,\n                                                                onChange: (e)=>setOptions({\n                                                                        ...options,\n                                                                        width: parseInt(e.target.value) || 256\n                                                                    }),\n                                                                min: \"128\",\n                                                                max: \"1024\",\n                                                                className: \"w-full px-3 py-2 outline-none text-sm text-gray-500 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-500 mb-1\",\n                                                                children: \"สีเข้ม\"\n                                                            }, void 0, false, {\n                                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"color\",\n                                                                value: options.color.dark,\n                                                                onChange: (e)=>setOptions({\n                                                                        ...options,\n                                                                        color: {\n                                                                            ...options.color,\n                                                                            dark: e.target.value\n                                                                        }\n                                                                    }),\n                                                                className: \"w-full h-10 border border-gray-300 rounded-md\"\n                                                            }, void 0, false, {\n                                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-500 mb-1\",\n                                                                children: \"สีอ่อน\"\n                                                            }, void 0, false, {\n                                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"color\",\n                                                                value: options.color.light,\n                                                                onChange: (e)=>setOptions({\n                                                                        ...options,\n                                                                        color: {\n                                                                            ...options.color,\n                                                                            light: e.target.value\n                                                                        }\n                                                                    }),\n                                                                className: \"w-full h-10 border border-gray-300 rounded-md\"\n                                                            }, void 0, false, {\n                                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: generateQRCode,\n                                        disabled: !text.trim() || isGenerating,\n                                        className: \"flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                        children: isGenerating ? 'กำลังสร้าง...' : 'สร้าง QR Code'\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: clearAll,\n                                        className: \"flex-1 bg-red-400 text-white py-3 px-6 rounded-lg font-medium hover:bg-red-500 transition-colors\",\n                                        children: \"ล้างข้อมูล\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center justify-center space-y-6\",\n                        children: qrCodeUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white p-4 rounded-lg shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: qrCodeUrl,\n                                        alt: \"Generated QR Code\",\n                                        width: options.width,\n                                        height: options.width,\n                                        className: \"max-w-full h-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-3 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: downloadQRCode,\n                                            className: \"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 mr-2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"ดาวน์โหลด\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: copyToClipboard,\n                                            className: \"flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 mr-2\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"คัดลอก\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center justify-center h-64 text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-16 h-16 mb-4\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1,\n                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg font-medium\",\n                                    children: \"QR Code จะแสดงที่นี่\"\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"ใส่ข้อความแล้วกดสร้าง QR Code\"\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                style: {\n                    display: 'none'\n                }\n            }, void 0, false, {\n                fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\components\\\\QRCodeGenerator.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(QRCodeGenerator, \"11LDzFFFY44y2vPDlJW43L0TaL8=\");\n_c = QRCodeGenerator;\nvar _c;\n$RefreshReg$(_c, \"QRCodeGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/QRCodeGenerator.tsx\n"));

/***/ })

});