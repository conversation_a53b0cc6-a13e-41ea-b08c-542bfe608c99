"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pngjs";
exports.ids = ["vendor-chunks/pngjs"];
exports.modules = {

/***/ "(ssr)/./node_modules/pngjs/lib/bitmapper.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitmapper.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(ssr)/./node_modules/pngjs/lib/interlace.js\");\n\nlet pixelBppMapper = [\n  // 0 - dummy entry\n  function () {},\n\n  // 1 - L\n  // 0: 0, 1: 0, 2: 0, 3: 0xff\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos === data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    let pixel = data[rawPos];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = 0xff;\n  },\n\n  // 2 - LA\n  // 0: 0, 1: 0, 2: 0, 3: 1\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos + 1 >= data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    let pixel = data[rawPos];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = data[rawPos + 1];\n  },\n\n  // 3 - RGB\n  // 0: 0, 1: 1, 2: 2, 3: 0xff\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos + 2 >= data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    pxData[pxPos] = data[rawPos];\n    pxData[pxPos + 1] = data[rawPos + 1];\n    pxData[pxPos + 2] = data[rawPos + 2];\n    pxData[pxPos + 3] = 0xff;\n  },\n\n  // 4 - RGBA\n  // 0: 0, 1: 1, 2: 2, 3: 3\n  function (pxData, data, pxPos, rawPos) {\n    if (rawPos + 3 >= data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n\n    pxData[pxPos] = data[rawPos];\n    pxData[pxPos + 1] = data[rawPos + 1];\n    pxData[pxPos + 2] = data[rawPos + 2];\n    pxData[pxPos + 3] = data[rawPos + 3];\n  },\n];\n\nlet pixelBppCustomMapper = [\n  // 0 - dummy entry\n  function () {},\n\n  // 1 - L\n  // 0: 0, 1: 0, 2: 0, 3: 0xff\n  function (pxData, pixelData, pxPos, maxBit) {\n    let pixel = pixelData[0];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = maxBit;\n  },\n\n  // 2 - LA\n  // 0: 0, 1: 0, 2: 0, 3: 1\n  function (pxData, pixelData, pxPos) {\n    let pixel = pixelData[0];\n    pxData[pxPos] = pixel;\n    pxData[pxPos + 1] = pixel;\n    pxData[pxPos + 2] = pixel;\n    pxData[pxPos + 3] = pixelData[1];\n  },\n\n  // 3 - RGB\n  // 0: 0, 1: 1, 2: 2, 3: 0xff\n  function (pxData, pixelData, pxPos, maxBit) {\n    pxData[pxPos] = pixelData[0];\n    pxData[pxPos + 1] = pixelData[1];\n    pxData[pxPos + 2] = pixelData[2];\n    pxData[pxPos + 3] = maxBit;\n  },\n\n  // 4 - RGBA\n  // 0: 0, 1: 1, 2: 2, 3: 3\n  function (pxData, pixelData, pxPos) {\n    pxData[pxPos] = pixelData[0];\n    pxData[pxPos + 1] = pixelData[1];\n    pxData[pxPos + 2] = pixelData[2];\n    pxData[pxPos + 3] = pixelData[3];\n  },\n];\n\nfunction bitRetriever(data, depth) {\n  let leftOver = [];\n  let i = 0;\n\n  function split() {\n    if (i === data.length) {\n      throw new Error(\"Ran out of data\");\n    }\n    let byte = data[i];\n    i++;\n    let byte8, byte7, byte6, byte5, byte4, byte3, byte2, byte1;\n    switch (depth) {\n      default:\n        throw new Error(\"unrecognised depth\");\n      case 16:\n        byte2 = data[i];\n        i++;\n        leftOver.push((byte << 8) + byte2);\n        break;\n      case 4:\n        byte2 = byte & 0x0f;\n        byte1 = byte >> 4;\n        leftOver.push(byte1, byte2);\n        break;\n      case 2:\n        byte4 = byte & 3;\n        byte3 = (byte >> 2) & 3;\n        byte2 = (byte >> 4) & 3;\n        byte1 = (byte >> 6) & 3;\n        leftOver.push(byte1, byte2, byte3, byte4);\n        break;\n      case 1:\n        byte8 = byte & 1;\n        byte7 = (byte >> 1) & 1;\n        byte6 = (byte >> 2) & 1;\n        byte5 = (byte >> 3) & 1;\n        byte4 = (byte >> 4) & 1;\n        byte3 = (byte >> 5) & 1;\n        byte2 = (byte >> 6) & 1;\n        byte1 = (byte >> 7) & 1;\n        leftOver.push(byte1, byte2, byte3, byte4, byte5, byte6, byte7, byte8);\n        break;\n    }\n  }\n\n  return {\n    get: function (count) {\n      while (leftOver.length < count) {\n        split();\n      }\n      let returner = leftOver.slice(0, count);\n      leftOver = leftOver.slice(count);\n      return returner;\n    },\n    resetAfterLine: function () {\n      leftOver.length = 0;\n    },\n    end: function () {\n      if (i !== data.length) {\n        throw new Error(\"extra data found\");\n      }\n    },\n  };\n}\n\nfunction mapImage8Bit(image, pxData, getPxPos, bpp, data, rawPos) {\n  // eslint-disable-line max-params\n  let imageWidth = image.width;\n  let imageHeight = image.height;\n  let imagePass = image.index;\n  for (let y = 0; y < imageHeight; y++) {\n    for (let x = 0; x < imageWidth; x++) {\n      let pxPos = getPxPos(x, y, imagePass);\n      pixelBppMapper[bpp](pxData, data, pxPos, rawPos);\n      rawPos += bpp; //eslint-disable-line no-param-reassign\n    }\n  }\n  return rawPos;\n}\n\nfunction mapImageCustomBit(image, pxData, getPxPos, bpp, bits, maxBit) {\n  // eslint-disable-line max-params\n  let imageWidth = image.width;\n  let imageHeight = image.height;\n  let imagePass = image.index;\n  for (let y = 0; y < imageHeight; y++) {\n    for (let x = 0; x < imageWidth; x++) {\n      let pixelData = bits.get(bpp);\n      let pxPos = getPxPos(x, y, imagePass);\n      pixelBppCustomMapper[bpp](pxData, pixelData, pxPos, maxBit);\n    }\n    bits.resetAfterLine();\n  }\n}\n\nexports.dataToBitMap = function (data, bitmapInfo) {\n  let width = bitmapInfo.width;\n  let height = bitmapInfo.height;\n  let depth = bitmapInfo.depth;\n  let bpp = bitmapInfo.bpp;\n  let interlace = bitmapInfo.interlace;\n  let bits;\n\n  if (depth !== 8) {\n    bits = bitRetriever(data, depth);\n  }\n  let pxData;\n  if (depth <= 8) {\n    pxData = Buffer.alloc(width * height * 4);\n  } else {\n    pxData = new Uint16Array(width * height * 4);\n  }\n  let maxBit = Math.pow(2, depth) - 1;\n  let rawPos = 0;\n  let images;\n  let getPxPos;\n\n  if (interlace) {\n    images = interlaceUtils.getImagePasses(width, height);\n    getPxPos = interlaceUtils.getInterlaceIterator(width, height);\n  } else {\n    let nonInterlacedPxPos = 0;\n    getPxPos = function () {\n      let returner = nonInterlacedPxPos;\n      nonInterlacedPxPos += 4;\n      return returner;\n    };\n    images = [{ width: width, height: height }];\n  }\n\n  for (let imageIndex = 0; imageIndex < images.length; imageIndex++) {\n    if (depth === 8) {\n      rawPos = mapImage8Bit(\n        images[imageIndex],\n        pxData,\n        getPxPos,\n        bpp,\n        data,\n        rawPos\n      );\n    } else {\n      mapImageCustomBit(\n        images[imageIndex],\n        pxData,\n        getPxPos,\n        bpp,\n        bits,\n        maxBit\n      );\n    }\n  }\n  if (depth === 8) {\n    if (rawPos !== data.length) {\n      throw new Error(\"extra data found\");\n    }\n  } else {\n    bits.end();\n  }\n\n  return pxData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/bitmapper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/bitpacker.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/bitpacker.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\n\nmodule.exports = function (dataIn, width, height, options) {\n  let outHasAlpha =\n    [constants.COLORTYPE_COLOR_ALPHA, constants.COLORTYPE_ALPHA].indexOf(\n      options.colorType\n    ) !== -1;\n  if (options.colorType === options.inputColorType) {\n    let bigEndian = (function () {\n      let buffer = new ArrayBuffer(2);\n      new DataView(buffer).setInt16(0, 256, true /* littleEndian */);\n      // Int16Array uses the platform's endianness.\n      return new Int16Array(buffer)[0] !== 256;\n    })();\n    // If no need to convert to grayscale and alpha is present/absent in both, take a fast route\n    if (options.bitDepth === 8 || (options.bitDepth === 16 && bigEndian)) {\n      return dataIn;\n    }\n  }\n\n  // map to a UInt16 array if data is 16bit, fix endianness below\n  let data = options.bitDepth !== 16 ? dataIn : new Uint16Array(dataIn.buffer);\n\n  let maxValue = 255;\n  let inBpp = constants.COLORTYPE_TO_BPP_MAP[options.inputColorType];\n  if (inBpp === 4 && !options.inputHasAlpha) {\n    inBpp = 3;\n  }\n  let outBpp = constants.COLORTYPE_TO_BPP_MAP[options.colorType];\n  if (options.bitDepth === 16) {\n    maxValue = 65535;\n    outBpp *= 2;\n  }\n  let outData = Buffer.alloc(width * height * outBpp);\n\n  let inIndex = 0;\n  let outIndex = 0;\n\n  let bgColor = options.bgColor || {};\n  if (bgColor.red === undefined) {\n    bgColor.red = maxValue;\n  }\n  if (bgColor.green === undefined) {\n    bgColor.green = maxValue;\n  }\n  if (bgColor.blue === undefined) {\n    bgColor.blue = maxValue;\n  }\n\n  function getRGBA() {\n    let red;\n    let green;\n    let blue;\n    let alpha = maxValue;\n    switch (options.inputColorType) {\n      case constants.COLORTYPE_COLOR_ALPHA:\n        alpha = data[inIndex + 3];\n        red = data[inIndex];\n        green = data[inIndex + 1];\n        blue = data[inIndex + 2];\n        break;\n      case constants.COLORTYPE_COLOR:\n        red = data[inIndex];\n        green = data[inIndex + 1];\n        blue = data[inIndex + 2];\n        break;\n      case constants.COLORTYPE_ALPHA:\n        alpha = data[inIndex + 1];\n        red = data[inIndex];\n        green = red;\n        blue = red;\n        break;\n      case constants.COLORTYPE_GRAYSCALE:\n        red = data[inIndex];\n        green = red;\n        blue = red;\n        break;\n      default:\n        throw new Error(\n          \"input color type:\" +\n            options.inputColorType +\n            \" is not supported at present\"\n        );\n    }\n\n    if (options.inputHasAlpha) {\n      if (!outHasAlpha) {\n        alpha /= maxValue;\n        red = Math.min(\n          Math.max(Math.round((1 - alpha) * bgColor.red + alpha * red), 0),\n          maxValue\n        );\n        green = Math.min(\n          Math.max(Math.round((1 - alpha) * bgColor.green + alpha * green), 0),\n          maxValue\n        );\n        blue = Math.min(\n          Math.max(Math.round((1 - alpha) * bgColor.blue + alpha * blue), 0),\n          maxValue\n        );\n      }\n    }\n    return { red: red, green: green, blue: blue, alpha: alpha };\n  }\n\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let rgba = getRGBA(data, inIndex);\n\n      switch (options.colorType) {\n        case constants.COLORTYPE_COLOR_ALPHA:\n        case constants.COLORTYPE_COLOR:\n          if (options.bitDepth === 8) {\n            outData[outIndex] = rgba.red;\n            outData[outIndex + 1] = rgba.green;\n            outData[outIndex + 2] = rgba.blue;\n            if (outHasAlpha) {\n              outData[outIndex + 3] = rgba.alpha;\n            }\n          } else {\n            outData.writeUInt16BE(rgba.red, outIndex);\n            outData.writeUInt16BE(rgba.green, outIndex + 2);\n            outData.writeUInt16BE(rgba.blue, outIndex + 4);\n            if (outHasAlpha) {\n              outData.writeUInt16BE(rgba.alpha, outIndex + 6);\n            }\n          }\n          break;\n        case constants.COLORTYPE_ALPHA:\n        case constants.COLORTYPE_GRAYSCALE: {\n          // Convert to grayscale and alpha\n          let grayscale = (rgba.red + rgba.green + rgba.blue) / 3;\n          if (options.bitDepth === 8) {\n            outData[outIndex] = grayscale;\n            if (outHasAlpha) {\n              outData[outIndex + 1] = rgba.alpha;\n            }\n          } else {\n            outData.writeUInt16BE(grayscale, outIndex);\n            if (outHasAlpha) {\n              outData.writeUInt16BE(rgba.alpha, outIndex + 2);\n            }\n          }\n          break;\n        }\n        default:\n          throw new Error(\"unrecognised color Type \" + options.colorType);\n      }\n\n      inIndex += inBpp;\n      outIndex += outBpp;\n    }\n  }\n\n  return outData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/bitpacker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/chunkstream.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/chunkstream.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\n\nlet ChunkStream = (module.exports = function () {\n  Stream.call(this);\n\n  this._buffers = [];\n  this._buffered = 0;\n\n  this._reads = [];\n  this._paused = false;\n\n  this._encoding = \"utf8\";\n  this.writable = true;\n});\nutil.inherits(ChunkStream, Stream);\n\nChunkStream.prototype.read = function (length, callback) {\n  this._reads.push({\n    length: Math.abs(length), // if length < 0 then at most this length\n    allowLess: length < 0,\n    func: callback,\n  });\n\n  process.nextTick(\n    function () {\n      this._process();\n\n      // its paused and there is not enought data then ask for more\n      if (this._paused && this._reads && this._reads.length > 0) {\n        this._paused = false;\n\n        this.emit(\"drain\");\n      }\n    }.bind(this)\n  );\n};\n\nChunkStream.prototype.write = function (data, encoding) {\n  if (!this.writable) {\n    this.emit(\"error\", new Error(\"Stream not writable\"));\n    return false;\n  }\n\n  let dataBuffer;\n  if (Buffer.isBuffer(data)) {\n    dataBuffer = data;\n  } else {\n    dataBuffer = Buffer.from(data, encoding || this._encoding);\n  }\n\n  this._buffers.push(dataBuffer);\n  this._buffered += dataBuffer.length;\n\n  this._process();\n\n  // ok if there are no more read requests\n  if (this._reads && this._reads.length === 0) {\n    this._paused = true;\n  }\n\n  return this.writable && !this._paused;\n};\n\nChunkStream.prototype.end = function (data, encoding) {\n  if (data) {\n    this.write(data, encoding);\n  }\n\n  this.writable = false;\n\n  // already destroyed\n  if (!this._buffers) {\n    return;\n  }\n\n  // enqueue or handle end\n  if (this._buffers.length === 0) {\n    this._end();\n  } else {\n    this._buffers.push(null);\n    this._process();\n  }\n};\n\nChunkStream.prototype.destroySoon = ChunkStream.prototype.end;\n\nChunkStream.prototype._end = function () {\n  if (this._reads.length > 0) {\n    this.emit(\"error\", new Error(\"Unexpected end of input\"));\n  }\n\n  this.destroy();\n};\n\nChunkStream.prototype.destroy = function () {\n  if (!this._buffers) {\n    return;\n  }\n\n  this.writable = false;\n  this._reads = null;\n  this._buffers = null;\n\n  this.emit(\"close\");\n};\n\nChunkStream.prototype._processReadAllowingLess = function (read) {\n  // ok there is any data so that we can satisfy this request\n  this._reads.shift(); // == read\n\n  // first we need to peek into first buffer\n  let smallerBuf = this._buffers[0];\n\n  // ok there is more data than we need\n  if (smallerBuf.length > read.length) {\n    this._buffered -= read.length;\n    this._buffers[0] = smallerBuf.slice(read.length);\n\n    read.func.call(this, smallerBuf.slice(0, read.length));\n  } else {\n    // ok this is less than maximum length so use it all\n    this._buffered -= smallerBuf.length;\n    this._buffers.shift(); // == smallerBuf\n\n    read.func.call(this, smallerBuf);\n  }\n};\n\nChunkStream.prototype._processRead = function (read) {\n  this._reads.shift(); // == read\n\n  let pos = 0;\n  let count = 0;\n  let data = Buffer.alloc(read.length);\n\n  // create buffer for all data\n  while (pos < read.length) {\n    let buf = this._buffers[count++];\n    let len = Math.min(buf.length, read.length - pos);\n\n    buf.copy(data, pos, 0, len);\n    pos += len;\n\n    // last buffer wasn't used all so just slice it and leave\n    if (len !== buf.length) {\n      this._buffers[--count] = buf.slice(len);\n    }\n  }\n\n  // remove all used buffers\n  if (count > 0) {\n    this._buffers.splice(0, count);\n  }\n\n  this._buffered -= read.length;\n\n  read.func.call(this, data);\n};\n\nChunkStream.prototype._process = function () {\n  try {\n    // as long as there is any data and read requests\n    while (this._buffered > 0 && this._reads && this._reads.length > 0) {\n      let read = this._reads[0];\n\n      // read any data (but no more than length)\n      if (read.allowLess) {\n        this._processReadAllowingLess(read);\n      } else if (this._buffered >= read.length) {\n        // ok we can meet some expectations\n\n        this._processRead(read);\n      } else {\n        // not enought data to satisfy first request in queue\n        // so we need to wait for more\n        break;\n      }\n    }\n\n    if (this._buffers && !this.writable) {\n      this._end();\n    }\n  } catch (ex) {\n    this.emit(\"error\", ex);\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/chunkstream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/constants.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/constants.js ***!
  \*********************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = {\n  PNG_SIGNATURE: [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a],\n\n  TYPE_IHDR: 0x49484452,\n  TYPE_IEND: 0x49454e44,\n  TYPE_IDAT: 0x49444154,\n  TYPE_PLTE: 0x504c5445,\n  TYPE_tRNS: 0x74524e53, // eslint-disable-line camelcase\n  TYPE_gAMA: 0x67414d41, // eslint-disable-line camelcase\n\n  // color-type bits\n  COLORTYPE_GRAYSCALE: 0,\n  COLORTYPE_PALETTE: 1,\n  COLORTYPE_COLOR: 2,\n  COLORTYPE_ALPHA: 4, // e.g. grayscale and alpha\n\n  // color-type combinations\n  COLORTYPE_PALETTE_COLOR: 3,\n  COLORTYPE_COLOR_ALPHA: 6,\n\n  COLORTYPE_TO_BPP_MAP: {\n    0: 1,\n    2: 3,\n    3: 1,\n    4: 2,\n    6: 4,\n  },\n\n  GAMMA_DIVISION: 100000,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EiLCJzb3VyY2VzIjpbIkg6XFxQUk9KRUNUXFxRUmNvZGVcXG5vZGVfbW9kdWxlc1xccG5nanNcXGxpYlxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgUE5HX1NJR05BVFVSRTogWzB4ODksIDB4NTAsIDB4NGUsIDB4NDcsIDB4MGQsIDB4MGEsIDB4MWEsIDB4MGFdLFxuXG4gIFRZUEVfSUhEUjogMHg0OTQ4NDQ1MixcbiAgVFlQRV9JRU5EOiAweDQ5NDU0ZTQ0LFxuICBUWVBFX0lEQVQ6IDB4NDk0NDQxNTQsXG4gIFRZUEVfUExURTogMHg1MDRjNTQ0NSxcbiAgVFlQRV90Uk5TOiAweDc0NTI0ZTUzLCAvLyBlc2xpbnQtZGlzYWJsZS1saW5lIGNhbWVsY2FzZVxuICBUWVBFX2dBTUE6IDB4Njc0MTRkNDEsIC8vIGVzbGludC1kaXNhYmxlLWxpbmUgY2FtZWxjYXNlXG5cbiAgLy8gY29sb3ItdHlwZSBiaXRzXG4gIENPTE9SVFlQRV9HUkFZU0NBTEU6IDAsXG4gIENPTE9SVFlQRV9QQUxFVFRFOiAxLFxuICBDT0xPUlRZUEVfQ09MT1I6IDIsXG4gIENPTE9SVFlQRV9BTFBIQTogNCwgLy8gZS5nLiBncmF5c2NhbGUgYW5kIGFscGhhXG5cbiAgLy8gY29sb3ItdHlwZSBjb21iaW5hdGlvbnNcbiAgQ09MT1JUWVBFX1BBTEVUVEVfQ09MT1I6IDMsXG4gIENPTE9SVFlQRV9DT0xPUl9BTFBIQTogNixcblxuICBDT0xPUlRZUEVfVE9fQlBQX01BUDoge1xuICAgIDA6IDEsXG4gICAgMjogMyxcbiAgICAzOiAxLFxuICAgIDQ6IDIsXG4gICAgNjogNCxcbiAgfSxcblxuICBHQU1NQV9ESVZJU0lPTjogMTAwMDAwLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/crc.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/crc.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nlet crcTable = [];\n\n(function () {\n  for (let i = 0; i < 256; i++) {\n    let currentCrc = i;\n    for (let j = 0; j < 8; j++) {\n      if (currentCrc & 1) {\n        currentCrc = 0xedb88320 ^ (currentCrc >>> 1);\n      } else {\n        currentCrc = currentCrc >>> 1;\n      }\n    }\n    crcTable[i] = currentCrc;\n  }\n})();\n\nlet CrcCalculator = (module.exports = function () {\n  this._crc = -1;\n});\n\nCrcCalculator.prototype.write = function (data) {\n  for (let i = 0; i < data.length; i++) {\n    this._crc = crcTable[(this._crc ^ data[i]) & 0xff] ^ (this._crc >>> 8);\n  }\n  return true;\n};\n\nCrcCalculator.prototype.crc32 = function () {\n  return this._crc ^ -1;\n};\n\nCrcCalculator.crc32 = function (buf) {\n  let crc = -1;\n  for (let i = 0; i < buf.length; i++) {\n    crc = crcTable[(crc ^ buf[i]) & 0xff] ^ (crc >>> 8);\n  }\n  return crc ^ -1;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2NyYy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjs7QUFFQTtBQUNBLGtCQUFrQixTQUFTO0FBQzNCO0FBQ0Esb0JBQW9CLE9BQU87QUFDM0I7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBLGtCQUFrQixpQkFBaUI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxrQkFBa0IsZ0JBQWdCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJIOlxcUFJPSkVDVFxcUVJjb2RlXFxub2RlX21vZHVsZXNcXHBuZ2pzXFxsaWJcXGNyYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubGV0IGNyY1RhYmxlID0gW107XG5cbihmdW5jdGlvbiAoKSB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgMjU2OyBpKyspIHtcbiAgICBsZXQgY3VycmVudENyYyA9IGk7XG4gICAgZm9yIChsZXQgaiA9IDA7IGogPCA4OyBqKyspIHtcbiAgICAgIGlmIChjdXJyZW50Q3JjICYgMSkge1xuICAgICAgICBjdXJyZW50Q3JjID0gMHhlZGI4ODMyMCBeIChjdXJyZW50Q3JjID4+PiAxKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGN1cnJlbnRDcmMgPSBjdXJyZW50Q3JjID4+PiAxO1xuICAgICAgfVxuICAgIH1cbiAgICBjcmNUYWJsZVtpXSA9IGN1cnJlbnRDcmM7XG4gIH1cbn0pKCk7XG5cbmxldCBDcmNDYWxjdWxhdG9yID0gKG1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKCkge1xuICB0aGlzLl9jcmMgPSAtMTtcbn0pO1xuXG5DcmNDYWxjdWxhdG9yLnByb3RvdHlwZS53cml0ZSA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgZGF0YS5sZW5ndGg7IGkrKykge1xuICAgIHRoaXMuX2NyYyA9IGNyY1RhYmxlWyh0aGlzLl9jcmMgXiBkYXRhW2ldKSAmIDB4ZmZdIF4gKHRoaXMuX2NyYyA+Pj4gOCk7XG4gIH1cbiAgcmV0dXJuIHRydWU7XG59O1xuXG5DcmNDYWxjdWxhdG9yLnByb3RvdHlwZS5jcmMzMiA9IGZ1bmN0aW9uICgpIHtcbiAgcmV0dXJuIHRoaXMuX2NyYyBeIC0xO1xufTtcblxuQ3JjQ2FsY3VsYXRvci5jcmMzMiA9IGZ1bmN0aW9uIChidWYpIHtcbiAgbGV0IGNyYyA9IC0xO1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGJ1Zi5sZW5ndGg7IGkrKykge1xuICAgIGNyYyA9IGNyY1RhYmxlWyhjcmMgXiBidWZbaV0pICYgMHhmZl0gXiAoY3JjID4+PiA4KTtcbiAgfVxuICByZXR1cm4gY3JjIF4gLTE7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/crc.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-pack.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/filter-pack.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\");\n\nfunction filterNone(pxData, pxPos, byteWidth, rawData, rawPos) {\n  for (let x = 0; x < byteWidth; x++) {\n    rawData[rawPos + x] = pxData[pxPos + x];\n  }\n}\n\nfunction filterSumNone(pxData, pxPos, byteWidth) {\n  let sum = 0;\n  let length = pxPos + byteWidth;\n\n  for (let i = pxPos; i < length; i++) {\n    sum += Math.abs(pxData[i]);\n  }\n  return sum;\n}\n\nfunction filterSub(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let val = pxData[pxPos + x] - left;\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumSub(pxData, pxPos, byteWidth, bpp) {\n  let sum = 0;\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let val = pxData[pxPos + x] - left;\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterUp(pxData, pxPos, byteWidth, rawData, rawPos) {\n  for (let x = 0; x < byteWidth; x++) {\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let val = pxData[pxPos + x] - up;\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumUp(pxData, pxPos, byteWidth) {\n  let sum = 0;\n  let length = pxPos + byteWidth;\n  for (let x = pxPos; x < length; x++) {\n    let up = pxPos > 0 ? pxData[x - byteWidth] : 0;\n    let val = pxData[x] - up;\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterAvg(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let val = pxData[pxPos + x] - ((left + up) >> 1);\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumAvg(pxData, pxPos, byteWidth, bpp) {\n  let sum = 0;\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let val = pxData[pxPos + x] - ((left + up) >> 1);\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nfunction filterPaeth(pxData, pxPos, byteWidth, rawData, rawPos, bpp) {\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let upleft =\n      pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n    let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n\n    rawData[rawPos + x] = val;\n  }\n}\n\nfunction filterSumPaeth(pxData, pxPos, byteWidth, bpp) {\n  let sum = 0;\n  for (let x = 0; x < byteWidth; x++) {\n    let left = x >= bpp ? pxData[pxPos + x - bpp] : 0;\n    let up = pxPos > 0 ? pxData[pxPos + x - byteWidth] : 0;\n    let upleft =\n      pxPos > 0 && x >= bpp ? pxData[pxPos + x - (byteWidth + bpp)] : 0;\n    let val = pxData[pxPos + x] - paethPredictor(left, up, upleft);\n\n    sum += Math.abs(val);\n  }\n\n  return sum;\n}\n\nlet filters = {\n  0: filterNone,\n  1: filterSub,\n  2: filterUp,\n  3: filterAvg,\n  4: filterPaeth,\n};\n\nlet filterSums = {\n  0: filterSumNone,\n  1: filterSumSub,\n  2: filterSumUp,\n  3: filterSumAvg,\n  4: filterSumPaeth,\n};\n\nmodule.exports = function (pxData, width, height, options, bpp) {\n  let filterTypes;\n  if (!(\"filterType\" in options) || options.filterType === -1) {\n    filterTypes = [0, 1, 2, 3, 4];\n  } else if (typeof options.filterType === \"number\") {\n    filterTypes = [options.filterType];\n  } else {\n    throw new Error(\"unrecognised filter types\");\n  }\n\n  if (options.bitDepth === 16) {\n    bpp *= 2;\n  }\n  let byteWidth = width * bpp;\n  let rawPos = 0;\n  let pxPos = 0;\n  let rawData = Buffer.alloc((byteWidth + 1) * height);\n\n  let sel = filterTypes[0];\n\n  for (let y = 0; y < height; y++) {\n    if (filterTypes.length > 1) {\n      // find best filter for this line (with lowest sum of values)\n      let min = Infinity;\n\n      for (let i = 0; i < filterTypes.length; i++) {\n        let sum = filterSums[filterTypes[i]](pxData, pxPos, byteWidth, bpp);\n        if (sum < min) {\n          sel = filterTypes[i];\n          min = sum;\n        }\n      }\n    }\n\n    rawData[rawPos] = sel;\n    rawPos++;\n    filters[sel](pxData, pxPos, byteWidth, rawData, rawPos, bpp);\n    rawPos += byteWidth;\n    pxPos += byteWidth;\n  }\n  return rawData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-pack.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse-async.js":
/*!******************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-async.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(ssr)/./node_modules/pngjs/lib/chunkstream.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(ssr)/./node_modules/pngjs/lib/filter-parse.js\");\n\nlet FilterAsync = (module.exports = function (bitmapInfo) {\n  ChunkStream.call(this);\n\n  let buffers = [];\n  let that = this;\n  this._filter = new Filter(bitmapInfo, {\n    read: this.read.bind(this),\n    write: function (buffer) {\n      buffers.push(buffer);\n    },\n    complete: function () {\n      that.emit(\"complete\", Buffer.concat(buffers));\n    },\n  });\n\n  this._filter.start();\n});\nutil.inherits(FilterAsync, ChunkStream);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS1hc3luYy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixXQUFXLG1CQUFPLENBQUMsa0JBQU07QUFDekIsa0JBQWtCLG1CQUFPLENBQUMsb0VBQWU7QUFDekMsYUFBYSxtQkFBTyxDQUFDLHNFQUFnQjs7QUFFckM7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHOztBQUVIO0FBQ0EsQ0FBQztBQUNEIiwic291cmNlcyI6WyJIOlxcUFJPSkVDVFxcUVJjb2RlXFxub2RlX21vZHVsZXNcXHBuZ2pzXFxsaWJcXGZpbHRlci1wYXJzZS1hc3luYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxubGV0IHV0aWwgPSByZXF1aXJlKFwidXRpbFwiKTtcbmxldCBDaHVua1N0cmVhbSA9IHJlcXVpcmUoXCIuL2NodW5rc3RyZWFtXCIpO1xubGV0IEZpbHRlciA9IHJlcXVpcmUoXCIuL2ZpbHRlci1wYXJzZVwiKTtcblxubGV0IEZpbHRlckFzeW5jID0gKG1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKGJpdG1hcEluZm8pIHtcbiAgQ2h1bmtTdHJlYW0uY2FsbCh0aGlzKTtcblxuICBsZXQgYnVmZmVycyA9IFtdO1xuICBsZXQgdGhhdCA9IHRoaXM7XG4gIHRoaXMuX2ZpbHRlciA9IG5ldyBGaWx0ZXIoYml0bWFwSW5mbywge1xuICAgIHJlYWQ6IHRoaXMucmVhZC5iaW5kKHRoaXMpLFxuICAgIHdyaXRlOiBmdW5jdGlvbiAoYnVmZmVyKSB7XG4gICAgICBidWZmZXJzLnB1c2goYnVmZmVyKTtcbiAgICB9LFxuICAgIGNvbXBsZXRlOiBmdW5jdGlvbiAoKSB7XG4gICAgICB0aGF0LmVtaXQoXCJjb21wbGV0ZVwiLCBCdWZmZXIuY29uY2F0KGJ1ZmZlcnMpKTtcbiAgICB9LFxuICB9KTtcblxuICB0aGlzLl9maWx0ZXIuc3RhcnQoKTtcbn0pO1xudXRpbC5pbmhlcml0cyhGaWx0ZXJBc3luYywgQ2h1bmtTdHJlYW0pO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse-sync.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(ssr)/./node_modules/pngjs/lib/sync-reader.js\");\nlet Filter = __webpack_require__(/*! ./filter-parse */ \"(ssr)/./node_modules/pngjs/lib/filter-parse.js\");\n\nexports.process = function (inBuffer, bitmapInfo) {\n  let outBuffers = [];\n  let reader = new SyncReader(inBuffer);\n  let filter = new Filter(bitmapInfo, {\n    read: reader.read.bind(reader),\n    write: function (bufferPart) {\n      outBuffers.push(bufferPart);\n    },\n    complete: function () {},\n  });\n\n  filter.start();\n  reader.process();\n\n  return Buffer.concat(outBuffers);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2ZpbHRlci1wYXJzZS1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGlCQUFpQixtQkFBTyxDQUFDLG9FQUFlO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyxzRUFBZ0I7O0FBRXJDLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsNEJBQTRCO0FBQzVCLEdBQUc7O0FBRUg7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJIOlxcUFJPSkVDVFxcUVJjb2RlXFxub2RlX21vZHVsZXNcXHBuZ2pzXFxsaWJcXGZpbHRlci1wYXJzZS1zeW5jLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5sZXQgU3luY1JlYWRlciA9IHJlcXVpcmUoXCIuL3N5bmMtcmVhZGVyXCIpO1xubGV0IEZpbHRlciA9IHJlcXVpcmUoXCIuL2ZpbHRlci1wYXJzZVwiKTtcblxuZXhwb3J0cy5wcm9jZXNzID0gZnVuY3Rpb24gKGluQnVmZmVyLCBiaXRtYXBJbmZvKSB7XG4gIGxldCBvdXRCdWZmZXJzID0gW107XG4gIGxldCByZWFkZXIgPSBuZXcgU3luY1JlYWRlcihpbkJ1ZmZlcik7XG4gIGxldCBmaWx0ZXIgPSBuZXcgRmlsdGVyKGJpdG1hcEluZm8sIHtcbiAgICByZWFkOiByZWFkZXIucmVhZC5iaW5kKHJlYWRlciksXG4gICAgd3JpdGU6IGZ1bmN0aW9uIChidWZmZXJQYXJ0KSB7XG4gICAgICBvdXRCdWZmZXJzLnB1c2goYnVmZmVyUGFydCk7XG4gICAgfSxcbiAgICBjb21wbGV0ZTogZnVuY3Rpb24gKCkge30sXG4gIH0pO1xuXG4gIGZpbHRlci5zdGFydCgpO1xuICByZWFkZXIucHJvY2VzcygpO1xuXG4gIHJldHVybiBCdWZmZXIuY29uY2F0KG91dEJ1ZmZlcnMpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/filter-parse.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/filter-parse.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet interlaceUtils = __webpack_require__(/*! ./interlace */ \"(ssr)/./node_modules/pngjs/lib/interlace.js\");\nlet paethPredictor = __webpack_require__(/*! ./paeth-predictor */ \"(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\");\n\nfunction getByteWidth(width, bpp, depth) {\n  let byteWidth = width * bpp;\n  if (depth !== 8) {\n    byteWidth = Math.ceil(byteWidth / (8 / depth));\n  }\n  return byteWidth;\n}\n\nlet Filter = (module.exports = function (bitmapInfo, dependencies) {\n  let width = bitmapInfo.width;\n  let height = bitmapInfo.height;\n  let interlace = bitmapInfo.interlace;\n  let bpp = bitmapInfo.bpp;\n  let depth = bitmapInfo.depth;\n\n  this.read = dependencies.read;\n  this.write = dependencies.write;\n  this.complete = dependencies.complete;\n\n  this._imageIndex = 0;\n  this._images = [];\n  if (interlace) {\n    let passes = interlaceUtils.getImagePasses(width, height);\n    for (let i = 0; i < passes.length; i++) {\n      this._images.push({\n        byteWidth: getByteWidth(passes[i].width, bpp, depth),\n        height: passes[i].height,\n        lineIndex: 0,\n      });\n    }\n  } else {\n    this._images.push({\n      byteWidth: getByteWidth(width, bpp, depth),\n      height: height,\n      lineIndex: 0,\n    });\n  }\n\n  // when filtering the line we look at the pixel to the left\n  // the spec also says it is done on a byte level regardless of the number of pixels\n  // so if the depth is byte compatible (8 or 16) we subtract the bpp in order to compare back\n  // a pixel rather than just a different byte part. However if we are sub byte, we ignore.\n  if (depth === 8) {\n    this._xComparison = bpp;\n  } else if (depth === 16) {\n    this._xComparison = bpp * 2;\n  } else {\n    this._xComparison = 1;\n  }\n});\n\nFilter.prototype.start = function () {\n  this.read(\n    this._images[this._imageIndex].byteWidth + 1,\n    this._reverseFilterLine.bind(this)\n  );\n};\n\nFilter.prototype._unFilterType1 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let xComparison = this._xComparison;\n  let xBiggerThan = xComparison - 1;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f1Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    unfilteredLine[x] = rawByte + f1Left;\n  }\n};\n\nFilter.prototype._unFilterType2 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let lastLine = this._lastLine;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f2Up = lastLine ? lastLine[x] : 0;\n    unfilteredLine[x] = rawByte + f2Up;\n  }\n};\n\nFilter.prototype._unFilterType3 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let xComparison = this._xComparison;\n  let xBiggerThan = xComparison - 1;\n  let lastLine = this._lastLine;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f3Up = lastLine ? lastLine[x] : 0;\n    let f3Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    let f3Add = Math.floor((f3Left + f3Up) / 2);\n    unfilteredLine[x] = rawByte + f3Add;\n  }\n};\n\nFilter.prototype._unFilterType4 = function (\n  rawData,\n  unfilteredLine,\n  byteWidth\n) {\n  let xComparison = this._xComparison;\n  let xBiggerThan = xComparison - 1;\n  let lastLine = this._lastLine;\n\n  for (let x = 0; x < byteWidth; x++) {\n    let rawByte = rawData[1 + x];\n    let f4Up = lastLine ? lastLine[x] : 0;\n    let f4Left = x > xBiggerThan ? unfilteredLine[x - xComparison] : 0;\n    let f4UpLeft = x > xBiggerThan && lastLine ? lastLine[x - xComparison] : 0;\n    let f4Add = paethPredictor(f4Left, f4Up, f4UpLeft);\n    unfilteredLine[x] = rawByte + f4Add;\n  }\n};\n\nFilter.prototype._reverseFilterLine = function (rawData) {\n  let filter = rawData[0];\n  let unfilteredLine;\n  let currentImage = this._images[this._imageIndex];\n  let byteWidth = currentImage.byteWidth;\n\n  if (filter === 0) {\n    unfilteredLine = rawData.slice(1, byteWidth + 1);\n  } else {\n    unfilteredLine = Buffer.alloc(byteWidth);\n\n    switch (filter) {\n      case 1:\n        this._unFilterType1(rawData, unfilteredLine, byteWidth);\n        break;\n      case 2:\n        this._unFilterType2(rawData, unfilteredLine, byteWidth);\n        break;\n      case 3:\n        this._unFilterType3(rawData, unfilteredLine, byteWidth);\n        break;\n      case 4:\n        this._unFilterType4(rawData, unfilteredLine, byteWidth);\n        break;\n      default:\n        throw new Error(\"Unrecognised filter type - \" + filter);\n    }\n  }\n\n  this.write(unfilteredLine);\n\n  currentImage.lineIndex++;\n  if (currentImage.lineIndex >= currentImage.height) {\n    this._lastLine = null;\n    this._imageIndex++;\n    currentImage = this._images[this._imageIndex];\n  } else {\n    this._lastLine = unfilteredLine;\n  }\n\n  if (currentImage) {\n    // read, using the byte width that may be from the new current image\n    this.read(currentImage.byteWidth + 1, this._reverseFilterLine.bind(this));\n  } else {\n    this._lastLine = null;\n    this.complete();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/filter-parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/format-normaliser.js":
/*!*****************************************************!*\
  !*** ./node_modules/pngjs/lib/format-normaliser.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("\n\nfunction dePalette(indata, outdata, width, height, palette) {\n  let pxPos = 0;\n  // use values from palette\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let color = palette[indata[pxPos]];\n\n      if (!color) {\n        throw new Error(\"index \" + indata[pxPos] + \" not in palette\");\n      }\n\n      for (let i = 0; i < 4; i++) {\n        outdata[pxPos + i] = color[i];\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nfunction replaceTransparentColor(indata, outdata, width, height, transColor) {\n  let pxPos = 0;\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      let makeTrans = false;\n\n      if (transColor.length === 1) {\n        if (transColor[0] === indata[pxPos]) {\n          makeTrans = true;\n        }\n      } else if (\n        transColor[0] === indata[pxPos] &&\n        transColor[1] === indata[pxPos + 1] &&\n        transColor[2] === indata[pxPos + 2]\n      ) {\n        makeTrans = true;\n      }\n      if (makeTrans) {\n        for (let i = 0; i < 4; i++) {\n          outdata[pxPos + i] = 0;\n        }\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nfunction scaleDepth(indata, outdata, width, height, depth) {\n  let maxOutSample = 255;\n  let maxInSample = Math.pow(2, depth) - 1;\n  let pxPos = 0;\n\n  for (let y = 0; y < height; y++) {\n    for (let x = 0; x < width; x++) {\n      for (let i = 0; i < 4; i++) {\n        outdata[pxPos + i] = Math.floor(\n          (indata[pxPos + i] * maxOutSample) / maxInSample + 0.5\n        );\n      }\n      pxPos += 4;\n    }\n  }\n}\n\nmodule.exports = function (indata, imageData) {\n  let depth = imageData.depth;\n  let width = imageData.width;\n  let height = imageData.height;\n  let colorType = imageData.colorType;\n  let transColor = imageData.transColor;\n  let palette = imageData.palette;\n\n  let outdata = indata; // only different for 16 bits\n\n  if (colorType === 3) {\n    // paletted\n    dePalette(indata, outdata, width, height, palette);\n  } else {\n    if (transColor) {\n      replaceTransparentColor(indata, outdata, width, height, transColor);\n    }\n    // if it needs scaling\n    if (depth !== 8) {\n      // if we need to change the buffer size\n      if (depth === 16) {\n        outdata = Buffer.alloc(width * height * 4);\n      }\n      scaleDepth(indata, outdata, width, height, depth);\n    }\n  }\n  return outdata;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL2Zvcm1hdC1ub3JtYWxpc2VyLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixZQUFZO0FBQzlCLG9CQUFvQixXQUFXO0FBQy9COztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxzQkFBc0IsT0FBTztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGtCQUFrQixZQUFZO0FBQzlCLG9CQUFvQixXQUFXO0FBQy9COztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLE9BQU87QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsa0JBQWtCLFlBQVk7QUFDOUIsb0JBQW9CLFdBQVc7QUFDL0Isc0JBQXNCLE9BQU87QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx3QkFBd0I7O0FBRXhCO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJIOlxcUFJPSkVDVFxcUVJjb2RlXFxub2RlX21vZHVsZXNcXHBuZ2pzXFxsaWJcXGZvcm1hdC1ub3JtYWxpc2VyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5mdW5jdGlvbiBkZVBhbGV0dGUoaW5kYXRhLCBvdXRkYXRhLCB3aWR0aCwgaGVpZ2h0LCBwYWxldHRlKSB7XG4gIGxldCBweFBvcyA9IDA7XG4gIC8vIHVzZSB2YWx1ZXMgZnJvbSBwYWxldHRlXG4gIGZvciAobGV0IHkgPSAwOyB5IDwgaGVpZ2h0OyB5KyspIHtcbiAgICBmb3IgKGxldCB4ID0gMDsgeCA8IHdpZHRoOyB4KyspIHtcbiAgICAgIGxldCBjb2xvciA9IHBhbGV0dGVbaW5kYXRhW3B4UG9zXV07XG5cbiAgICAgIGlmICghY29sb3IpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiaW5kZXggXCIgKyBpbmRhdGFbcHhQb3NdICsgXCIgbm90IGluIHBhbGV0dGVcIik7XG4gICAgICB9XG5cbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgNDsgaSsrKSB7XG4gICAgICAgIG91dGRhdGFbcHhQb3MgKyBpXSA9IGNvbG9yW2ldO1xuICAgICAgfVxuICAgICAgcHhQb3MgKz0gNDtcbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gcmVwbGFjZVRyYW5zcGFyZW50Q29sb3IoaW5kYXRhLCBvdXRkYXRhLCB3aWR0aCwgaGVpZ2h0LCB0cmFuc0NvbG9yKSB7XG4gIGxldCBweFBvcyA9IDA7XG4gIGZvciAobGV0IHkgPSAwOyB5IDwgaGVpZ2h0OyB5KyspIHtcbiAgICBmb3IgKGxldCB4ID0gMDsgeCA8IHdpZHRoOyB4KyspIHtcbiAgICAgIGxldCBtYWtlVHJhbnMgPSBmYWxzZTtcblxuICAgICAgaWYgKHRyYW5zQ29sb3IubGVuZ3RoID09PSAxKSB7XG4gICAgICAgIGlmICh0cmFuc0NvbG9yWzBdID09PSBpbmRhdGFbcHhQb3NdKSB7XG4gICAgICAgICAgbWFrZVRyYW5zID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChcbiAgICAgICAgdHJhbnNDb2xvclswXSA9PT0gaW5kYXRhW3B4UG9zXSAmJlxuICAgICAgICB0cmFuc0NvbG9yWzFdID09PSBpbmRhdGFbcHhQb3MgKyAxXSAmJlxuICAgICAgICB0cmFuc0NvbG9yWzJdID09PSBpbmRhdGFbcHhQb3MgKyAyXVxuICAgICAgKSB7XG4gICAgICAgIG1ha2VUcmFucyA9IHRydWU7XG4gICAgICB9XG4gICAgICBpZiAobWFrZVRyYW5zKSB7XG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgNDsgaSsrKSB7XG4gICAgICAgICAgb3V0ZGF0YVtweFBvcyArIGldID0gMDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcHhQb3MgKz0gNDtcbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gc2NhbGVEZXB0aChpbmRhdGEsIG91dGRhdGEsIHdpZHRoLCBoZWlnaHQsIGRlcHRoKSB7XG4gIGxldCBtYXhPdXRTYW1wbGUgPSAyNTU7XG4gIGxldCBtYXhJblNhbXBsZSA9IE1hdGgucG93KDIsIGRlcHRoKSAtIDE7XG4gIGxldCBweFBvcyA9IDA7XG5cbiAgZm9yIChsZXQgeSA9IDA7IHkgPCBoZWlnaHQ7IHkrKykge1xuICAgIGZvciAobGV0IHggPSAwOyB4IDwgd2lkdGg7IHgrKykge1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCA0OyBpKyspIHtcbiAgICAgICAgb3V0ZGF0YVtweFBvcyArIGldID0gTWF0aC5mbG9vcihcbiAgICAgICAgICAoaW5kYXRhW3B4UG9zICsgaV0gKiBtYXhPdXRTYW1wbGUpIC8gbWF4SW5TYW1wbGUgKyAwLjVcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIHB4UG9zICs9IDQ7XG4gICAgfVxuICB9XG59XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKGluZGF0YSwgaW1hZ2VEYXRhKSB7XG4gIGxldCBkZXB0aCA9IGltYWdlRGF0YS5kZXB0aDtcbiAgbGV0IHdpZHRoID0gaW1hZ2VEYXRhLndpZHRoO1xuICBsZXQgaGVpZ2h0ID0gaW1hZ2VEYXRhLmhlaWdodDtcbiAgbGV0IGNvbG9yVHlwZSA9IGltYWdlRGF0YS5jb2xvclR5cGU7XG4gIGxldCB0cmFuc0NvbG9yID0gaW1hZ2VEYXRhLnRyYW5zQ29sb3I7XG4gIGxldCBwYWxldHRlID0gaW1hZ2VEYXRhLnBhbGV0dGU7XG5cbiAgbGV0IG91dGRhdGEgPSBpbmRhdGE7IC8vIG9ubHkgZGlmZmVyZW50IGZvciAxNiBiaXRzXG5cbiAgaWYgKGNvbG9yVHlwZSA9PT0gMykge1xuICAgIC8vIHBhbGV0dGVkXG4gICAgZGVQYWxldHRlKGluZGF0YSwgb3V0ZGF0YSwgd2lkdGgsIGhlaWdodCwgcGFsZXR0ZSk7XG4gIH0gZWxzZSB7XG4gICAgaWYgKHRyYW5zQ29sb3IpIHtcbiAgICAgIHJlcGxhY2VUcmFuc3BhcmVudENvbG9yKGluZGF0YSwgb3V0ZGF0YSwgd2lkdGgsIGhlaWdodCwgdHJhbnNDb2xvcik7XG4gICAgfVxuICAgIC8vIGlmIGl0IG5lZWRzIHNjYWxpbmdcbiAgICBpZiAoZGVwdGggIT09IDgpIHtcbiAgICAgIC8vIGlmIHdlIG5lZWQgdG8gY2hhbmdlIHRoZSBidWZmZXIgc2l6ZVxuICAgICAgaWYgKGRlcHRoID09PSAxNikge1xuICAgICAgICBvdXRkYXRhID0gQnVmZmVyLmFsbG9jKHdpZHRoICogaGVpZ2h0ICogNCk7XG4gICAgICB9XG4gICAgICBzY2FsZURlcHRoKGluZGF0YSwgb3V0ZGF0YSwgd2lkdGgsIGhlaWdodCwgZGVwdGgpO1xuICAgIH1cbiAgfVxuICByZXR1cm4gb3V0ZGF0YTtcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/format-normaliser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/interlace.js":
/*!*********************************************!*\
  !*** ./node_modules/pngjs/lib/interlace.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\n// Adam 7\n//   0 1 2 3 4 5 6 7\n// 0 x 6 4 6 x 6 4 6\n// 1 7 7 7 7 7 7 7 7\n// 2 5 6 5 6 5 6 5 6\n// 3 7 7 7 7 7 7 7 7\n// 4 3 6 4 6 3 6 4 6\n// 5 7 7 7 7 7 7 7 7\n// 6 5 6 5 6 5 6 5 6\n// 7 7 7 7 7 7 7 7 7\n\nlet imagePasses = [\n  {\n    // pass 1 - 1px\n    x: [0],\n    y: [0],\n  },\n  {\n    // pass 2 - 1px\n    x: [4],\n    y: [0],\n  },\n  {\n    // pass 3 - 2px\n    x: [0, 4],\n    y: [4],\n  },\n  {\n    // pass 4 - 4px\n    x: [2, 6],\n    y: [0, 4],\n  },\n  {\n    // pass 5 - 8px\n    x: [0, 2, 4, 6],\n    y: [2, 6],\n  },\n  {\n    // pass 6 - 16px\n    x: [1, 3, 5, 7],\n    y: [0, 2, 4, 6],\n  },\n  {\n    // pass 7 - 32px\n    x: [0, 1, 2, 3, 4, 5, 6, 7],\n    y: [1, 3, 5, 7],\n  },\n];\n\nexports.getImagePasses = function (width, height) {\n  let images = [];\n  let xLeftOver = width % 8;\n  let yLeftOver = height % 8;\n  let xRepeats = (width - xLeftOver) / 8;\n  let yRepeats = (height - yLeftOver) / 8;\n  for (let i = 0; i < imagePasses.length; i++) {\n    let pass = imagePasses[i];\n    let passWidth = xRepeats * pass.x.length;\n    let passHeight = yRepeats * pass.y.length;\n    for (let j = 0; j < pass.x.length; j++) {\n      if (pass.x[j] < xLeftOver) {\n        passWidth++;\n      } else {\n        break;\n      }\n    }\n    for (let j = 0; j < pass.y.length; j++) {\n      if (pass.y[j] < yLeftOver) {\n        passHeight++;\n      } else {\n        break;\n      }\n    }\n    if (passWidth > 0 && passHeight > 0) {\n      images.push({ width: passWidth, height: passHeight, index: i });\n    }\n  }\n  return images;\n};\n\nexports.getInterlaceIterator = function (width) {\n  return function (x, y, pass) {\n    let outerXLeftOver = x % imagePasses[pass].x.length;\n    let outerX =\n      ((x - outerXLeftOver) / imagePasses[pass].x.length) * 8 +\n      imagePasses[pass].x[outerXLeftOver];\n    let outerYLeftOver = y % imagePasses[pass].y.length;\n    let outerY =\n      ((y - outerYLeftOver) / imagePasses[pass].y.length) * 8 +\n      imagePasses[pass].y[outerYLeftOver];\n    return outerX * 4 + outerY * width * 4;\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/interlace.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/packer-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(ssr)/./node_modules/pngjs/lib/packer.js\");\n\nlet PackerAsync = (module.exports = function (opt) {\n  Stream.call(this);\n\n  let options = opt || {};\n\n  this._packer = new Packer(options);\n  this._deflate = this._packer.createDeflate();\n\n  this.readable = true;\n});\nutil.inherits(PackerAsync, Stream);\n\nPackerAsync.prototype.pack = function (data, width, height, gamma) {\n  // Signature\n  this.emit(\"data\", Buffer.from(constants.PNG_SIGNATURE));\n  this.emit(\"data\", this._packer.packIHDR(width, height));\n\n  if (gamma) {\n    this.emit(\"data\", this._packer.packGAMA(gamma));\n  }\n\n  let filteredData = this._packer.filterData(data, width, height);\n\n  // compress it\n  this._deflate.on(\"error\", this.emit.bind(this, \"error\"));\n\n  this._deflate.on(\n    \"data\",\n    function (compressedData) {\n      this.emit(\"data\", this._packer.packIDAT(compressedData));\n    }.bind(this)\n  );\n\n  this._deflate.on(\n    \"end\",\n    function () {\n      this.emit(\"data\", this._packer.packIEND());\n      this.emit(\"end\");\n    }.bind(this)\n  );\n\n  this._deflate.end(filteredData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BhY2tlci1hc3luYy5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixXQUFXLG1CQUFPLENBQUMsa0JBQU07QUFDekIsYUFBYSxtQkFBTyxDQUFDLHNCQUFRO0FBQzdCLGdCQUFnQixtQkFBTyxDQUFDLGdFQUFhO0FBQ3JDLGFBQWEsbUJBQU8sQ0FBQywwREFBVTs7QUFFL0I7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsQ0FBQztBQUNEOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiSDpcXFBST0pFQ1RcXFFSY29kZVxcbm9kZV9tb2R1bGVzXFxwbmdqc1xcbGliXFxwYWNrZXItYXN5bmMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmxldCB1dGlsID0gcmVxdWlyZShcInV0aWxcIik7XG5sZXQgU3RyZWFtID0gcmVxdWlyZShcInN0cmVhbVwiKTtcbmxldCBjb25zdGFudHMgPSByZXF1aXJlKFwiLi9jb25zdGFudHNcIik7XG5sZXQgUGFja2VyID0gcmVxdWlyZShcIi4vcGFja2VyXCIpO1xuXG5sZXQgUGFja2VyQXN5bmMgPSAobW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiAob3B0KSB7XG4gIFN0cmVhbS5jYWxsKHRoaXMpO1xuXG4gIGxldCBvcHRpb25zID0gb3B0IHx8IHt9O1xuXG4gIHRoaXMuX3BhY2tlciA9IG5ldyBQYWNrZXIob3B0aW9ucyk7XG4gIHRoaXMuX2RlZmxhdGUgPSB0aGlzLl9wYWNrZXIuY3JlYXRlRGVmbGF0ZSgpO1xuXG4gIHRoaXMucmVhZGFibGUgPSB0cnVlO1xufSk7XG51dGlsLmluaGVyaXRzKFBhY2tlckFzeW5jLCBTdHJlYW0pO1xuXG5QYWNrZXJBc3luYy5wcm90b3R5cGUucGFjayA9IGZ1bmN0aW9uIChkYXRhLCB3aWR0aCwgaGVpZ2h0LCBnYW1tYSkge1xuICAvLyBTaWduYXR1cmVcbiAgdGhpcy5lbWl0KFwiZGF0YVwiLCBCdWZmZXIuZnJvbShjb25zdGFudHMuUE5HX1NJR05BVFVSRSkpO1xuICB0aGlzLmVtaXQoXCJkYXRhXCIsIHRoaXMuX3BhY2tlci5wYWNrSUhEUih3aWR0aCwgaGVpZ2h0KSk7XG5cbiAgaWYgKGdhbW1hKSB7XG4gICAgdGhpcy5lbWl0KFwiZGF0YVwiLCB0aGlzLl9wYWNrZXIucGFja0dBTUEoZ2FtbWEpKTtcbiAgfVxuXG4gIGxldCBmaWx0ZXJlZERhdGEgPSB0aGlzLl9wYWNrZXIuZmlsdGVyRGF0YShkYXRhLCB3aWR0aCwgaGVpZ2h0KTtcblxuICAvLyBjb21wcmVzcyBpdFxuICB0aGlzLl9kZWZsYXRlLm9uKFwiZXJyb3JcIiwgdGhpcy5lbWl0LmJpbmQodGhpcywgXCJlcnJvclwiKSk7XG5cbiAgdGhpcy5fZGVmbGF0ZS5vbihcbiAgICBcImRhdGFcIixcbiAgICBmdW5jdGlvbiAoY29tcHJlc3NlZERhdGEpIHtcbiAgICAgIHRoaXMuZW1pdChcImRhdGFcIiwgdGhpcy5fcGFja2VyLnBhY2tJREFUKGNvbXByZXNzZWREYXRhKSk7XG4gICAgfS5iaW5kKHRoaXMpXG4gICk7XG5cbiAgdGhpcy5fZGVmbGF0ZS5vbihcbiAgICBcImVuZFwiLFxuICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgIHRoaXMuZW1pdChcImRhdGFcIiwgdGhpcy5fcGFja2VyLnBhY2tJRU5EKCkpO1xuICAgICAgdGhpcy5lbWl0KFwiZW5kXCIpO1xuICAgIH0uYmluZCh0aGlzKVxuICApO1xuXG4gIHRoaXMuX2RlZmxhdGUuZW5kKGZpbHRlcmVkRGF0YSk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/packer-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nif (!zlib.deflateSync) {\n  hasSyncZlib = false;\n}\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet Packer = __webpack_require__(/*! ./packer */ \"(ssr)/./node_modules/pngjs/lib/packer.js\");\n\nmodule.exports = function (metaData, opt) {\n  if (!hasSyncZlib) {\n    throw new Error(\n      \"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\"\n    );\n  }\n\n  let options = opt || {};\n\n  let packer = new Packer(options);\n\n  let chunks = [];\n\n  // Signature\n  chunks.push(Buffer.from(constants.PNG_SIGNATURE));\n\n  // Header\n  chunks.push(packer.packIHDR(metaData.width, metaData.height));\n\n  if (metaData.gamma) {\n    chunks.push(packer.packGAMA(metaData.gamma));\n  }\n\n  let filteredData = packer.filterData(\n    metaData.data,\n    metaData.width,\n    metaData.height\n  );\n\n  // compress it\n  let compressedData = zlib.deflateSync(\n    filteredData,\n    packer.getDeflateOptions()\n  );\n  filteredData = null;\n\n  if (!compressedData || !compressedData.length) {\n    throw new Error(\"bad png - invalid compressed data response\");\n  }\n  chunks.push(packer.packIDAT(compressedData));\n\n  // End\n  chunks.push(packer.packIEND());\n\n  return Buffer.concat(chunks);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/packer.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/packer.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet CrcStream = __webpack_require__(/*! ./crc */ \"(ssr)/./node_modules/pngjs/lib/crc.js\");\nlet bitPacker = __webpack_require__(/*! ./bitpacker */ \"(ssr)/./node_modules/pngjs/lib/bitpacker.js\");\nlet filter = __webpack_require__(/*! ./filter-pack */ \"(ssr)/./node_modules/pngjs/lib/filter-pack.js\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\n\nlet Packer = (module.exports = function (options) {\n  this._options = options;\n\n  options.deflateChunkSize = options.deflateChunkSize || 32 * 1024;\n  options.deflateLevel =\n    options.deflateLevel != null ? options.deflateLevel : 9;\n  options.deflateStrategy =\n    options.deflateStrategy != null ? options.deflateStrategy : 3;\n  options.inputHasAlpha =\n    options.inputHasAlpha != null ? options.inputHasAlpha : true;\n  options.deflateFactory = options.deflateFactory || zlib.createDeflate;\n  options.bitDepth = options.bitDepth || 8;\n  // This is outputColorType\n  options.colorType =\n    typeof options.colorType === \"number\"\n      ? options.colorType\n      : constants.COLORTYPE_COLOR_ALPHA;\n  options.inputColorType =\n    typeof options.inputColorType === \"number\"\n      ? options.inputColorType\n      : constants.COLORTYPE_COLOR_ALPHA;\n\n  if (\n    [\n      constants.COLORTYPE_GRAYSCALE,\n      constants.COLORTYPE_COLOR,\n      constants.COLORTYPE_COLOR_ALPHA,\n      constants.COLORTYPE_ALPHA,\n    ].indexOf(options.colorType) === -1\n  ) {\n    throw new Error(\n      \"option color type:\" + options.colorType + \" is not supported at present\"\n    );\n  }\n  if (\n    [\n      constants.COLORTYPE_GRAYSCALE,\n      constants.COLORTYPE_COLOR,\n      constants.COLORTYPE_COLOR_ALPHA,\n      constants.COLORTYPE_ALPHA,\n    ].indexOf(options.inputColorType) === -1\n  ) {\n    throw new Error(\n      \"option input color type:\" +\n        options.inputColorType +\n        \" is not supported at present\"\n    );\n  }\n  if (options.bitDepth !== 8 && options.bitDepth !== 16) {\n    throw new Error(\n      \"option bit depth:\" + options.bitDepth + \" is not supported at present\"\n    );\n  }\n});\n\nPacker.prototype.getDeflateOptions = function () {\n  return {\n    chunkSize: this._options.deflateChunkSize,\n    level: this._options.deflateLevel,\n    strategy: this._options.deflateStrategy,\n  };\n};\n\nPacker.prototype.createDeflate = function () {\n  return this._options.deflateFactory(this.getDeflateOptions());\n};\n\nPacker.prototype.filterData = function (data, width, height) {\n  // convert to correct format for filtering (e.g. right bpp and bit depth)\n  let packedData = bitPacker(data, width, height, this._options);\n\n  // filter pixel data\n  let bpp = constants.COLORTYPE_TO_BPP_MAP[this._options.colorType];\n  let filteredData = filter(packedData, width, height, this._options, bpp);\n  return filteredData;\n};\n\nPacker.prototype._packChunk = function (type, data) {\n  let len = data ? data.length : 0;\n  let buf = Buffer.alloc(len + 12);\n\n  buf.writeUInt32BE(len, 0);\n  buf.writeUInt32BE(type, 4);\n\n  if (data) {\n    data.copy(buf, 8);\n  }\n\n  buf.writeInt32BE(\n    CrcStream.crc32(buf.slice(4, buf.length - 4)),\n    buf.length - 4\n  );\n  return buf;\n};\n\nPacker.prototype.packGAMA = function (gamma) {\n  let buf = Buffer.alloc(4);\n  buf.writeUInt32BE(Math.floor(gamma * constants.GAMMA_DIVISION), 0);\n  return this._packChunk(constants.TYPE_gAMA, buf);\n};\n\nPacker.prototype.packIHDR = function (width, height) {\n  let buf = Buffer.alloc(13);\n  buf.writeUInt32BE(width, 0);\n  buf.writeUInt32BE(height, 4);\n  buf[8] = this._options.bitDepth; // Bit depth\n  buf[9] = this._options.colorType; // colorType\n  buf[10] = 0; // compression\n  buf[11] = 0; // filter\n  buf[12] = 0; // interlace\n\n  return this._packChunk(constants.TYPE_IHDR, buf);\n};\n\nPacker.prototype.packIDAT = function (data) {\n  return this._packChunk(constants.TYPE_IDAT, data);\n};\n\nPacker.prototype.packIEND = function () {\n  return this._packChunk(constants.TYPE_IEND, null);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/packer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/paeth-predictor.js":
/*!***************************************************!*\
  !*** ./node_modules/pngjs/lib/paeth-predictor.js ***!
  \***************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = function paethPredictor(left, above, upLeft) {\n  let paeth = left + above - upLeft;\n  let pLeft = Math.abs(paeth - left);\n  let pAbove = Math.abs(paeth - above);\n  let pUpLeft = Math.abs(paeth - upLeft);\n\n  if (pLeft <= pAbove && pLeft <= pUpLeft) {\n    return left;\n  }\n  if (pAbove <= pUpLeft) {\n    return above;\n  }\n  return upLeft;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BhZXRoLXByZWRpY3Rvci5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkg6XFxQUk9KRUNUXFxRUmNvZGVcXG5vZGVfbW9kdWxlc1xccG5nanNcXGxpYlxccGFldGgtcHJlZGljdG9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIHBhZXRoUHJlZGljdG9yKGxlZnQsIGFib3ZlLCB1cExlZnQpIHtcbiAgbGV0IHBhZXRoID0gbGVmdCArIGFib3ZlIC0gdXBMZWZ0O1xuICBsZXQgcExlZnQgPSBNYXRoLmFicyhwYWV0aCAtIGxlZnQpO1xuICBsZXQgcEFib3ZlID0gTWF0aC5hYnMocGFldGggLSBhYm92ZSk7XG4gIGxldCBwVXBMZWZ0ID0gTWF0aC5hYnMocGFldGggLSB1cExlZnQpO1xuXG4gIGlmIChwTGVmdCA8PSBwQWJvdmUgJiYgcExlZnQgPD0gcFVwTGVmdCkge1xuICAgIHJldHVybiBsZWZ0O1xuICB9XG4gIGlmIChwQWJvdmUgPD0gcFVwTGVmdCkge1xuICAgIHJldHVybiBhYm92ZTtcbiAgfVxuICByZXR1cm4gdXBMZWZ0O1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/paeth-predictor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser-async.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/parser-async.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet ChunkStream = __webpack_require__(/*! ./chunkstream */ \"(ssr)/./node_modules/pngjs/lib/chunkstream.js\");\nlet FilterAsync = __webpack_require__(/*! ./filter-parse-async */ \"(ssr)/./node_modules/pngjs/lib/filter-parse-async.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(ssr)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(ssr)/./node_modules/pngjs/lib/format-normaliser.js\");\n\nlet ParserAsync = (module.exports = function (options) {\n  ChunkStream.call(this);\n\n  this._parser = new Parser(options, {\n    read: this.read.bind(this),\n    error: this._handleError.bind(this),\n    metadata: this._handleMetaData.bind(this),\n    gamma: this.emit.bind(this, \"gamma\"),\n    palette: this._handlePalette.bind(this),\n    transColor: this._handleTransColor.bind(this),\n    finished: this._finished.bind(this),\n    inflateData: this._inflateData.bind(this),\n    simpleTransparency: this._simpleTransparency.bind(this),\n    headersFinished: this._headersFinished.bind(this),\n  });\n  this._options = options;\n  this.writable = true;\n\n  this._parser.start();\n});\nutil.inherits(ParserAsync, ChunkStream);\n\nParserAsync.prototype._handleError = function (err) {\n  this.emit(\"error\", err);\n\n  this.writable = false;\n\n  this.destroy();\n\n  if (this._inflate && this._inflate.destroy) {\n    this._inflate.destroy();\n  }\n\n  if (this._filter) {\n    this._filter.destroy();\n    // For backward compatibility with Node 7 and below.\n    // Suppress errors due to _inflate calling write() even after\n    // it's destroy()'ed.\n    this._filter.on(\"error\", function () {});\n  }\n\n  this.errord = true;\n};\n\nParserAsync.prototype._inflateData = function (data) {\n  if (!this._inflate) {\n    if (this._bitmapInfo.interlace) {\n      this._inflate = zlib.createInflate();\n\n      this._inflate.on(\"error\", this.emit.bind(this, \"error\"));\n      this._filter.on(\"complete\", this._complete.bind(this));\n\n      this._inflate.pipe(this._filter);\n    } else {\n      let rowSize =\n        ((this._bitmapInfo.width *\n          this._bitmapInfo.bpp *\n          this._bitmapInfo.depth +\n          7) >>\n          3) +\n        1;\n      let imageSize = rowSize * this._bitmapInfo.height;\n      let chunkSize = Math.max(imageSize, zlib.Z_MIN_CHUNK);\n\n      this._inflate = zlib.createInflate({ chunkSize: chunkSize });\n      let leftToInflate = imageSize;\n\n      let emitError = this.emit.bind(this, \"error\");\n      this._inflate.on(\"error\", function (err) {\n        if (!leftToInflate) {\n          return;\n        }\n\n        emitError(err);\n      });\n      this._filter.on(\"complete\", this._complete.bind(this));\n\n      let filterWrite = this._filter.write.bind(this._filter);\n      this._inflate.on(\"data\", function (chunk) {\n        if (!leftToInflate) {\n          return;\n        }\n\n        if (chunk.length > leftToInflate) {\n          chunk = chunk.slice(0, leftToInflate);\n        }\n\n        leftToInflate -= chunk.length;\n\n        filterWrite(chunk);\n      });\n\n      this._inflate.on(\"end\", this._filter.end.bind(this._filter));\n    }\n  }\n  this._inflate.write(data);\n};\n\nParserAsync.prototype._handleMetaData = function (metaData) {\n  this._metaData = metaData;\n  this._bitmapInfo = Object.create(metaData);\n\n  this._filter = new FilterAsync(this._bitmapInfo);\n};\n\nParserAsync.prototype._handleTransColor = function (transColor) {\n  this._bitmapInfo.transColor = transColor;\n};\n\nParserAsync.prototype._handlePalette = function (palette) {\n  this._bitmapInfo.palette = palette;\n};\n\nParserAsync.prototype._simpleTransparency = function () {\n  this._metaData.alpha = true;\n};\n\nParserAsync.prototype._headersFinished = function () {\n  // Up until this point, we don't know if we have a tRNS chunk (alpha)\n  // so we can't emit metadata any earlier\n  this.emit(\"metadata\", this._metaData);\n};\n\nParserAsync.prototype._finished = function () {\n  if (this.errord) {\n    return;\n  }\n\n  if (!this._inflate) {\n    this.emit(\"error\", \"No Inflate block\");\n  } else {\n    // no more data to inflate\n    this._inflate.end();\n  }\n};\n\nParserAsync.prototype._complete = function (filteredData) {\n  if (this.errord) {\n    return;\n  }\n\n  let normalisedBitmapData;\n\n  try {\n    let bitmapData = bitmapper.dataToBitMap(filteredData, this._bitmapInfo);\n\n    normalisedBitmapData = formatNormaliser(bitmapData, this._bitmapInfo);\n    bitmapData = null;\n  } catch (ex) {\n    this._handleError(ex);\n    return;\n  }\n\n  this.emit(\"parsed\", normalisedBitmapData);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser-async.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser-sync.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/parser-sync.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet hasSyncZlib = true;\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet inflateSync = __webpack_require__(/*! ./sync-inflate */ \"(ssr)/./node_modules/pngjs/lib/sync-inflate.js\");\nif (!zlib.deflateSync) {\n  hasSyncZlib = false;\n}\nlet SyncReader = __webpack_require__(/*! ./sync-reader */ \"(ssr)/./node_modules/pngjs/lib/sync-reader.js\");\nlet FilterSync = __webpack_require__(/*! ./filter-parse-sync */ \"(ssr)/./node_modules/pngjs/lib/filter-parse-sync.js\");\nlet Parser = __webpack_require__(/*! ./parser */ \"(ssr)/./node_modules/pngjs/lib/parser.js\");\nlet bitmapper = __webpack_require__(/*! ./bitmapper */ \"(ssr)/./node_modules/pngjs/lib/bitmapper.js\");\nlet formatNormaliser = __webpack_require__(/*! ./format-normaliser */ \"(ssr)/./node_modules/pngjs/lib/format-normaliser.js\");\n\nmodule.exports = function (buffer, options) {\n  if (!hasSyncZlib) {\n    throw new Error(\n      \"To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0\"\n    );\n  }\n\n  let err;\n  function handleError(_err_) {\n    err = _err_;\n  }\n\n  let metaData;\n  function handleMetaData(_metaData_) {\n    metaData = _metaData_;\n  }\n\n  function handleTransColor(transColor) {\n    metaData.transColor = transColor;\n  }\n\n  function handlePalette(palette) {\n    metaData.palette = palette;\n  }\n\n  function handleSimpleTransparency() {\n    metaData.alpha = true;\n  }\n\n  let gamma;\n  function handleGamma(_gamma_) {\n    gamma = _gamma_;\n  }\n\n  let inflateDataList = [];\n  function handleInflateData(inflatedData) {\n    inflateDataList.push(inflatedData);\n  }\n\n  let reader = new SyncReader(buffer);\n\n  let parser = new Parser(options, {\n    read: reader.read.bind(reader),\n    error: handleError,\n    metadata: handleMetaData,\n    gamma: handleGamma,\n    palette: handlePalette,\n    transColor: handleTransColor,\n    inflateData: handleInflateData,\n    simpleTransparency: handleSimpleTransparency,\n  });\n\n  parser.start();\n  reader.process();\n\n  if (err) {\n    throw err;\n  }\n\n  //join together the inflate datas\n  let inflateData = Buffer.concat(inflateDataList);\n  inflateDataList.length = 0;\n\n  let inflatedData;\n  if (metaData.interlace) {\n    inflatedData = zlib.inflateSync(inflateData);\n  } else {\n    let rowSize =\n      ((metaData.width * metaData.bpp * metaData.depth + 7) >> 3) + 1;\n    let imageSize = rowSize * metaData.height;\n    inflatedData = inflateSync(inflateData, {\n      chunkSize: imageSize,\n      maxLength: imageSize,\n    });\n  }\n  inflateData = null;\n\n  if (!inflatedData || !inflatedData.length) {\n    throw new Error(\"bad png - invalid inflate data response\");\n  }\n\n  let unfilteredData = FilterSync.process(inflatedData, metaData);\n  inflateData = null;\n\n  let bitmapData = bitmapper.dataToBitMap(unfilteredData, metaData);\n  unfilteredData = null;\n\n  let normalisedBitmapData = formatNormaliser(bitmapData, metaData);\n\n  metaData.data = normalisedBitmapData;\n  metaData.gamma = gamma || 0;\n\n  return metaData;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/parser.js":
/*!******************************************!*\
  !*** ./node_modules/pngjs/lib/parser.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nlet constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/pngjs/lib/constants.js\");\nlet CrcCalculator = __webpack_require__(/*! ./crc */ \"(ssr)/./node_modules/pngjs/lib/crc.js\");\n\nlet Parser = (module.exports = function (options, dependencies) {\n  this._options = options;\n  options.checkCRC = options.checkCRC !== false;\n\n  this._hasIHDR = false;\n  this._hasIEND = false;\n  this._emittedHeadersFinished = false;\n\n  // input flags/metadata\n  this._palette = [];\n  this._colorType = 0;\n\n  this._chunks = {};\n  this._chunks[constants.TYPE_IHDR] = this._handleIHDR.bind(this);\n  this._chunks[constants.TYPE_IEND] = this._handleIEND.bind(this);\n  this._chunks[constants.TYPE_IDAT] = this._handleIDAT.bind(this);\n  this._chunks[constants.TYPE_PLTE] = this._handlePLTE.bind(this);\n  this._chunks[constants.TYPE_tRNS] = this._handleTRNS.bind(this);\n  this._chunks[constants.TYPE_gAMA] = this._handleGAMA.bind(this);\n\n  this.read = dependencies.read;\n  this.error = dependencies.error;\n  this.metadata = dependencies.metadata;\n  this.gamma = dependencies.gamma;\n  this.transColor = dependencies.transColor;\n  this.palette = dependencies.palette;\n  this.parsed = dependencies.parsed;\n  this.inflateData = dependencies.inflateData;\n  this.finished = dependencies.finished;\n  this.simpleTransparency = dependencies.simpleTransparency;\n  this.headersFinished = dependencies.headersFinished || function () {};\n});\n\nParser.prototype.start = function () {\n  this.read(constants.PNG_SIGNATURE.length, this._parseSignature.bind(this));\n};\n\nParser.prototype._parseSignature = function (data) {\n  let signature = constants.PNG_SIGNATURE;\n\n  for (let i = 0; i < signature.length; i++) {\n    if (data[i] !== signature[i]) {\n      this.error(new Error(\"Invalid file signature\"));\n      return;\n    }\n  }\n  this.read(8, this._parseChunkBegin.bind(this));\n};\n\nParser.prototype._parseChunkBegin = function (data) {\n  // chunk content length\n  let length = data.readUInt32BE(0);\n\n  // chunk type\n  let type = data.readUInt32BE(4);\n  let name = \"\";\n  for (let i = 4; i < 8; i++) {\n    name += String.fromCharCode(data[i]);\n  }\n\n  //console.log('chunk ', name, length);\n\n  // chunk flags\n  let ancillary = Boolean(data[4] & 0x20); // or critical\n  //    priv = Boolean(data[5] & 0x20), // or public\n  //    safeToCopy = Boolean(data[7] & 0x20); // or unsafe\n\n  if (!this._hasIHDR && type !== constants.TYPE_IHDR) {\n    this.error(new Error(\"Expected IHDR on beggining\"));\n    return;\n  }\n\n  this._crc = new CrcCalculator();\n  this._crc.write(Buffer.from(name));\n\n  if (this._chunks[type]) {\n    return this._chunks[type](length);\n  }\n\n  if (!ancillary) {\n    this.error(new Error(\"Unsupported critical chunk type \" + name));\n    return;\n  }\n\n  this.read(length + 4, this._skipChunk.bind(this));\n};\n\nParser.prototype._skipChunk = function (/*data*/) {\n  this.read(8, this._parseChunkBegin.bind(this));\n};\n\nParser.prototype._handleChunkEnd = function () {\n  this.read(4, this._parseChunkEnd.bind(this));\n};\n\nParser.prototype._parseChunkEnd = function (data) {\n  let fileCrc = data.readInt32BE(0);\n  let calcCrc = this._crc.crc32();\n\n  // check CRC\n  if (this._options.checkCRC && calcCrc !== fileCrc) {\n    this.error(new Error(\"Crc error - \" + fileCrc + \" - \" + calcCrc));\n    return;\n  }\n\n  if (!this._hasIEND) {\n    this.read(8, this._parseChunkBegin.bind(this));\n  }\n};\n\nParser.prototype._handleIHDR = function (length) {\n  this.read(length, this._parseIHDR.bind(this));\n};\nParser.prototype._parseIHDR = function (data) {\n  this._crc.write(data);\n\n  let width = data.readUInt32BE(0);\n  let height = data.readUInt32BE(4);\n  let depth = data[8];\n  let colorType = data[9]; // bits: 1 palette, 2 color, 4 alpha\n  let compr = data[10];\n  let filter = data[11];\n  let interlace = data[12];\n\n  // console.log('    width', width, 'height', height,\n  //     'depth', depth, 'colorType', colorType,\n  //     'compr', compr, 'filter', filter, 'interlace', interlace\n  // );\n\n  if (\n    depth !== 8 &&\n    depth !== 4 &&\n    depth !== 2 &&\n    depth !== 1 &&\n    depth !== 16\n  ) {\n    this.error(new Error(\"Unsupported bit depth \" + depth));\n    return;\n  }\n  if (!(colorType in constants.COLORTYPE_TO_BPP_MAP)) {\n    this.error(new Error(\"Unsupported color type\"));\n    return;\n  }\n  if (compr !== 0) {\n    this.error(new Error(\"Unsupported compression method\"));\n    return;\n  }\n  if (filter !== 0) {\n    this.error(new Error(\"Unsupported filter method\"));\n    return;\n  }\n  if (interlace !== 0 && interlace !== 1) {\n    this.error(new Error(\"Unsupported interlace method\"));\n    return;\n  }\n\n  this._colorType = colorType;\n\n  let bpp = constants.COLORTYPE_TO_BPP_MAP[this._colorType];\n\n  this._hasIHDR = true;\n\n  this.metadata({\n    width: width,\n    height: height,\n    depth: depth,\n    interlace: Boolean(interlace),\n    palette: Boolean(colorType & constants.COLORTYPE_PALETTE),\n    color: Boolean(colorType & constants.COLORTYPE_COLOR),\n    alpha: Boolean(colorType & constants.COLORTYPE_ALPHA),\n    bpp: bpp,\n    colorType: colorType,\n  });\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handlePLTE = function (length) {\n  this.read(length, this._parsePLTE.bind(this));\n};\nParser.prototype._parsePLTE = function (data) {\n  this._crc.write(data);\n\n  let entries = Math.floor(data.length / 3);\n  // console.log('Palette:', entries);\n\n  for (let i = 0; i < entries; i++) {\n    this._palette.push([data[i * 3], data[i * 3 + 1], data[i * 3 + 2], 0xff]);\n  }\n\n  this.palette(this._palette);\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleTRNS = function (length) {\n  this.simpleTransparency();\n  this.read(length, this._parseTRNS.bind(this));\n};\nParser.prototype._parseTRNS = function (data) {\n  this._crc.write(data);\n\n  // palette\n  if (this._colorType === constants.COLORTYPE_PALETTE_COLOR) {\n    if (this._palette.length === 0) {\n      this.error(new Error(\"Transparency chunk must be after palette\"));\n      return;\n    }\n    if (data.length > this._palette.length) {\n      this.error(new Error(\"More transparent colors than palette size\"));\n      return;\n    }\n    for (let i = 0; i < data.length; i++) {\n      this._palette[i][3] = data[i];\n    }\n    this.palette(this._palette);\n  }\n\n  // for colorType 0 (grayscale) and 2 (rgb)\n  // there might be one gray/color defined as transparent\n  if (this._colorType === constants.COLORTYPE_GRAYSCALE) {\n    // grey, 2 bytes\n    this.transColor([data.readUInt16BE(0)]);\n  }\n  if (this._colorType === constants.COLORTYPE_COLOR) {\n    this.transColor([\n      data.readUInt16BE(0),\n      data.readUInt16BE(2),\n      data.readUInt16BE(4),\n    ]);\n  }\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleGAMA = function (length) {\n  this.read(length, this._parseGAMA.bind(this));\n};\nParser.prototype._parseGAMA = function (data) {\n  this._crc.write(data);\n  this.gamma(data.readUInt32BE(0) / constants.GAMMA_DIVISION);\n\n  this._handleChunkEnd();\n};\n\nParser.prototype._handleIDAT = function (length) {\n  if (!this._emittedHeadersFinished) {\n    this._emittedHeadersFinished = true;\n    this.headersFinished();\n  }\n  this.read(-length, this._parseIDAT.bind(this, length));\n};\nParser.prototype._parseIDAT = function (length, data) {\n  this._crc.write(data);\n\n  if (\n    this._colorType === constants.COLORTYPE_PALETTE_COLOR &&\n    this._palette.length === 0\n  ) {\n    throw new Error(\"Expected palette not found\");\n  }\n\n  this.inflateData(data);\n  let leftOverLength = length - data.length;\n\n  if (leftOverLength > 0) {\n    this._handleIDAT(leftOverLength);\n  } else {\n    this._handleChunkEnd();\n  }\n};\n\nParser.prototype._handleIEND = function (length) {\n  this.read(length, this._parseIEND.bind(this));\n};\nParser.prototype._parseIEND = function (data) {\n  this._crc.write(data);\n\n  this._hasIEND = true;\n  this._handleChunkEnd();\n\n  if (this.finished) {\n    this.finished();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/png-sync.js":
/*!********************************************!*\
  !*** ./node_modules/pngjs/lib/png-sync.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet parse = __webpack_require__(/*! ./parser-sync */ \"(ssr)/./node_modules/pngjs/lib/parser-sync.js\");\nlet pack = __webpack_require__(/*! ./packer-sync */ \"(ssr)/./node_modules/pngjs/lib/packer-sync.js\");\n\nexports.read = function (buffer, options) {\n  return parse(buffer, options || {});\n};\n\nexports.write = function (png, options) {\n  return pack(png, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcG5nanMvbGliL3BuZy1zeW5jLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLFlBQVksbUJBQU8sQ0FBQyxvRUFBZTtBQUNuQyxXQUFXLG1CQUFPLENBQUMsb0VBQWU7O0FBRWxDLFlBQVk7QUFDWixvQ0FBb0M7QUFDcEM7O0FBRUEsYUFBYTtBQUNiO0FBQ0EiLCJzb3VyY2VzIjpbIkg6XFxQUk9KRUNUXFxRUmNvZGVcXG5vZGVfbW9kdWxlc1xccG5nanNcXGxpYlxccG5nLXN5bmMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbmxldCBwYXJzZSA9IHJlcXVpcmUoXCIuL3BhcnNlci1zeW5jXCIpO1xubGV0IHBhY2sgPSByZXF1aXJlKFwiLi9wYWNrZXItc3luY1wiKTtcblxuZXhwb3J0cy5yZWFkID0gZnVuY3Rpb24gKGJ1ZmZlciwgb3B0aW9ucykge1xuICByZXR1cm4gcGFyc2UoYnVmZmVyLCBvcHRpb25zIHx8IHt9KTtcbn07XG5cbmV4cG9ydHMud3JpdGUgPSBmdW5jdGlvbiAocG5nLCBvcHRpb25zKSB7XG4gIHJldHVybiBwYWNrKHBuZywgb3B0aW9ucyk7XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/png-sync.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/png.js":
/*!***************************************!*\
  !*** ./node_modules/pngjs/lib/png.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nlet util = __webpack_require__(/*! util */ \"util\");\nlet Stream = __webpack_require__(/*! stream */ \"stream\");\nlet Parser = __webpack_require__(/*! ./parser-async */ \"(ssr)/./node_modules/pngjs/lib/parser-async.js\");\nlet Packer = __webpack_require__(/*! ./packer-async */ \"(ssr)/./node_modules/pngjs/lib/packer-async.js\");\nlet PNGSync = __webpack_require__(/*! ./png-sync */ \"(ssr)/./node_modules/pngjs/lib/png-sync.js\");\n\nlet PNG = (exports.PNG = function (options) {\n  Stream.call(this);\n\n  options = options || {}; // eslint-disable-line no-param-reassign\n\n  // coerce pixel dimensions to integers (also coerces undefined -> 0):\n  this.width = options.width | 0;\n  this.height = options.height | 0;\n\n  this.data =\n    this.width > 0 && this.height > 0\n      ? Buffer.alloc(4 * this.width * this.height)\n      : null;\n\n  if (options.fill && this.data) {\n    this.data.fill(0);\n  }\n\n  this.gamma = 0;\n  this.readable = this.writable = true;\n\n  this._parser = new Parser(options);\n\n  this._parser.on(\"error\", this.emit.bind(this, \"error\"));\n  this._parser.on(\"close\", this._handleClose.bind(this));\n  this._parser.on(\"metadata\", this._metadata.bind(this));\n  this._parser.on(\"gamma\", this._gamma.bind(this));\n  this._parser.on(\n    \"parsed\",\n    function (data) {\n      this.data = data;\n      this.emit(\"parsed\", data);\n    }.bind(this)\n  );\n\n  this._packer = new Packer(options);\n  this._packer.on(\"data\", this.emit.bind(this, \"data\"));\n  this._packer.on(\"end\", this.emit.bind(this, \"end\"));\n  this._parser.on(\"close\", this._handleClose.bind(this));\n  this._packer.on(\"error\", this.emit.bind(this, \"error\"));\n});\nutil.inherits(PNG, Stream);\n\nPNG.sync = PNGSync;\n\nPNG.prototype.pack = function () {\n  if (!this.data || !this.data.length) {\n    this.emit(\"error\", \"No data provided\");\n    return this;\n  }\n\n  process.nextTick(\n    function () {\n      this._packer.pack(this.data, this.width, this.height, this.gamma);\n    }.bind(this)\n  );\n\n  return this;\n};\n\nPNG.prototype.parse = function (data, callback) {\n  if (callback) {\n    let onParsed, onError;\n\n    onParsed = function (parsedData) {\n      this.removeListener(\"error\", onError);\n\n      this.data = parsedData;\n      callback(null, this);\n    }.bind(this);\n\n    onError = function (err) {\n      this.removeListener(\"parsed\", onParsed);\n\n      callback(err, null);\n    }.bind(this);\n\n    this.once(\"parsed\", onParsed);\n    this.once(\"error\", onError);\n  }\n\n  this.end(data);\n  return this;\n};\n\nPNG.prototype.write = function (data) {\n  this._parser.write(data);\n  return true;\n};\n\nPNG.prototype.end = function (data) {\n  this._parser.end(data);\n};\n\nPNG.prototype._metadata = function (metadata) {\n  this.width = metadata.width;\n  this.height = metadata.height;\n\n  this.emit(\"metadata\", metadata);\n};\n\nPNG.prototype._gamma = function (gamma) {\n  this.gamma = gamma;\n};\n\nPNG.prototype._handleClose = function () {\n  if (!this._parser.writable && !this._packer.readable) {\n    this.emit(\"close\");\n  }\n};\n\nPNG.bitblt = function (src, dst, srcX, srcY, width, height, deltaX, deltaY) {\n  // eslint-disable-line max-params\n  // coerce pixel dimensions to integers (also coerces undefined -> 0):\n  /* eslint-disable no-param-reassign */\n  srcX |= 0;\n  srcY |= 0;\n  width |= 0;\n  height |= 0;\n  deltaX |= 0;\n  deltaY |= 0;\n  /* eslint-enable no-param-reassign */\n\n  if (\n    srcX > src.width ||\n    srcY > src.height ||\n    srcX + width > src.width ||\n    srcY + height > src.height\n  ) {\n    throw new Error(\"bitblt reading outside image\");\n  }\n\n  if (\n    deltaX > dst.width ||\n    deltaY > dst.height ||\n    deltaX + width > dst.width ||\n    deltaY + height > dst.height\n  ) {\n    throw new Error(\"bitblt writing outside image\");\n  }\n\n  for (let y = 0; y < height; y++) {\n    src.data.copy(\n      dst.data,\n      ((deltaY + y) * dst.width + deltaX) << 2,\n      ((srcY + y) * src.width + srcX) << 2,\n      ((srcY + y) * src.width + srcX + width) << 2\n    );\n  }\n};\n\nPNG.prototype.bitblt = function (\n  dst,\n  srcX,\n  srcY,\n  width,\n  height,\n  deltaX,\n  deltaY\n) {\n  // eslint-disable-line max-params\n\n  PNG.bitblt(this, dst, srcX, srcY, width, height, deltaX, deltaY);\n  return this;\n};\n\nPNG.adjustGamma = function (src) {\n  if (src.gamma) {\n    for (let y = 0; y < src.height; y++) {\n      for (let x = 0; x < src.width; x++) {\n        let idx = (src.width * y + x) << 2;\n\n        for (let i = 0; i < 3; i++) {\n          let sample = src.data[idx + i] / 255;\n          sample = Math.pow(sample, 1 / 2.2 / src.gamma);\n          src.data[idx + i] = Math.round(sample * 255);\n        }\n      }\n    }\n    src.gamma = 0;\n  }\n};\n\nPNG.prototype.adjustGamma = function () {\n  PNG.adjustGamma(this);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/png.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/sync-inflate.js":
/*!************************************************!*\
  !*** ./node_modules/pngjs/lib/sync-inflate.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\n\nlet assert = (__webpack_require__(/*! assert */ \"assert\").ok);\nlet zlib = __webpack_require__(/*! zlib */ \"zlib\");\nlet util = __webpack_require__(/*! util */ \"util\");\n\nlet kMaxLength = (__webpack_require__(/*! buffer */ \"buffer\").kMaxLength);\n\nfunction Inflate(opts) {\n  if (!(this instanceof Inflate)) {\n    return new Inflate(opts);\n  }\n\n  if (opts && opts.chunkSize < zlib.Z_MIN_CHUNK) {\n    opts.chunkSize = zlib.Z_MIN_CHUNK;\n  }\n\n  zlib.Inflate.call(this, opts);\n\n  // Node 8 --> 9 compatibility check\n  this._offset = this._offset === undefined ? this._outOffset : this._offset;\n  this._buffer = this._buffer || this._outBuffer;\n\n  if (opts && opts.maxLength != null) {\n    this._maxLength = opts.maxLength;\n  }\n}\n\nfunction createInflate(opts) {\n  return new Inflate(opts);\n}\n\nfunction _close(engine, callback) {\n  if (callback) {\n    process.nextTick(callback);\n  }\n\n  // Caller may invoke .close after a zlib error (which will null _handle).\n  if (!engine._handle) {\n    return;\n  }\n\n  engine._handle.close();\n  engine._handle = null;\n}\n\nInflate.prototype._processChunk = function (chunk, flushFlag, asyncCb) {\n  if (typeof asyncCb === \"function\") {\n    return zlib.Inflate._processChunk.call(this, chunk, flushFlag, asyncCb);\n  }\n\n  let self = this;\n\n  let availInBefore = chunk && chunk.length;\n  let availOutBefore = this._chunkSize - this._offset;\n  let leftToInflate = this._maxLength;\n  let inOff = 0;\n\n  let buffers = [];\n  let nread = 0;\n\n  let error;\n  this.on(\"error\", function (err) {\n    error = err;\n  });\n\n  function handleChunk(availInAfter, availOutAfter) {\n    if (self._hadError) {\n      return;\n    }\n\n    let have = availOutBefore - availOutAfter;\n    assert(have >= 0, \"have should not go down\");\n\n    if (have > 0) {\n      let out = self._buffer.slice(self._offset, self._offset + have);\n      self._offset += have;\n\n      if (out.length > leftToInflate) {\n        out = out.slice(0, leftToInflate);\n      }\n\n      buffers.push(out);\n      nread += out.length;\n      leftToInflate -= out.length;\n\n      if (leftToInflate === 0) {\n        return false;\n      }\n    }\n\n    if (availOutAfter === 0 || self._offset >= self._chunkSize) {\n      availOutBefore = self._chunkSize;\n      self._offset = 0;\n      self._buffer = Buffer.allocUnsafe(self._chunkSize);\n    }\n\n    if (availOutAfter === 0) {\n      inOff += availInBefore - availInAfter;\n      availInBefore = availInAfter;\n\n      return true;\n    }\n\n    return false;\n  }\n\n  assert(this._handle, \"zlib binding closed\");\n  let res;\n  do {\n    res = this._handle.writeSync(\n      flushFlag,\n      chunk, // in\n      inOff, // in_off\n      availInBefore, // in_len\n      this._buffer, // out\n      this._offset, //out_off\n      availOutBefore\n    ); // out_len\n    // Node 8 --> 9 compatibility check\n    res = res || this._writeState;\n  } while (!this._hadError && handleChunk(res[0], res[1]));\n\n  if (this._hadError) {\n    throw error;\n  }\n\n  if (nread >= kMaxLength) {\n    _close(this);\n    throw new RangeError(\n      \"Cannot create final Buffer. It would be larger than 0x\" +\n        kMaxLength.toString(16) +\n        \" bytes\"\n    );\n  }\n\n  let buf = Buffer.concat(buffers, nread);\n  _close(this);\n\n  return buf;\n};\n\nutil.inherits(Inflate, zlib.Inflate);\n\nfunction zlibBufferSync(engine, buffer) {\n  if (typeof buffer === \"string\") {\n    buffer = Buffer.from(buffer);\n  }\n  if (!(buffer instanceof Buffer)) {\n    throw new TypeError(\"Not a string or buffer\");\n  }\n\n  let flushFlag = engine._finishFlushFlag;\n  if (flushFlag == null) {\n    flushFlag = zlib.Z_FINISH;\n  }\n\n  return engine._processChunk(buffer, flushFlag);\n}\n\nfunction inflateSync(buffer, opts) {\n  return zlibBufferSync(new Inflate(opts), buffer);\n}\n\nmodule.exports = exports = inflateSync;\nexports.Inflate = Inflate;\nexports.createInflate = createInflate;\nexports.inflateSync = inflateSync;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/sync-inflate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/pngjs/lib/sync-reader.js":
/*!***********************************************!*\
  !*** ./node_modules/pngjs/lib/sync-reader.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\n\nlet SyncReader = (module.exports = function (buffer) {\n  this._buffer = buffer;\n  this._reads = [];\n});\n\nSyncReader.prototype.read = function (length, callback) {\n  this._reads.push({\n    length: Math.abs(length), // if length < 0 then at most this length\n    allowLess: length < 0,\n    func: callback,\n  });\n};\n\nSyncReader.prototype.process = function () {\n  // as long as there is any data and read requests\n  while (this._reads.length > 0 && this._buffer.length) {\n    let read = this._reads[0];\n\n    if (\n      this._buffer.length &&\n      (this._buffer.length >= read.length || read.allowLess)\n    ) {\n      // ok there is any data so that we can satisfy this request\n      this._reads.shift(); // == read\n\n      let buf = this._buffer;\n\n      this._buffer = buf.slice(read.length);\n\n      read.func.call(this, buf.slice(0, read.length));\n    } else {\n      break;\n    }\n  }\n\n  if (this._reads.length > 0) {\n    return new Error(\"There are some read requests waitng on finished stream\");\n  }\n\n  if (this._buffer.length > 0) {\n    return new Error(\"unrecognised content at end of stream\");\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/pngjs/lib/sync-reader.js\n");

/***/ })

};
;