(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{883:(e,r,t)=>{Promise.resolve().then(t.bind(t,3022))},3022:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var l=t(5155),s=t(3059),a=t.n(s),o=t(2115),n=t(6862),i=t(6766);function d(){let[e,r]=(0,o.useState)(""),[t,s]=(0,o.useState)(""),[a,d]=(0,o.useState)(!1),[c,x]=(0,o.useState)({errorCorrectionLevel:"M",type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"},width:256}),[m,u]=(0,o.useState)(!1),h=(0,o.useRef)(null),g=async()=>{if(e.trim()){d(!0);try{let r=await n.toDataURL(e,c);s(r),h.current&&await n.toCanvas(h.current,e,c)}catch(e){console.error("Error generating QR code:",e),alert("เกิดข้อผิดพลาดในการสร้าง QR Code")}finally{d(!1)}}},p=async()=>{if(t)try{let e=await fetch(t),r=await e.blob();await navigator.clipboard.write([new ClipboardItem({"image/png":r})]),alert("คัดลอก QR Code ไปยังคลิปบอร์ดแล้ว!")}catch(e){console.error("Error copying to clipboard:",e),alert("ไม่สามารถคัดลอกได้ กรุณาใช้ปุ่มดาวน์โหลดแทน")}};return(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"text-input",className:"block text-lg font-bold text-gray-500 mb-2",children:"ข้อความหรือ URL"}),(0,l.jsx)("textarea",{id:"text-input",value:e,onChange:e=>r(e.target.value),placeholder:"ใส่ข้อความ, URL, หรือข้อมูลที่ต้องการสร้าง QR Code...",className:"w-full h-32 px-4 py-3 outline-none text-gray-400 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",maxLength:2e3}),(0,l.jsxs)("div",{className:"text-right text-sm text-gray-500 mt-1",children:[e.length,"/2000"]})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("button",{onClick:()=>u(!m),className:"flex items-center text-md font-medium text-blue-400 hover:text-blue-500",children:[(0,l.jsx)("span",{children:"ตัวเลือกขั้นสูง"}),(0,l.jsx)("svg",{className:"ml-1 h-4 w-4 transform transition-transform ".concat(m?"rotate-180":""),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),m&&(0,l.jsxs)("div",{className:"mt-4 space-y-4 p-4 bg-gray-50 rounded-lg",children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-500 mb-1",children:"ระดับการแก้ไขข้อผิดพลาด"}),(0,l.jsxs)("select",{value:c.errorCorrectionLevel,onChange:e=>x({...c,errorCorrectionLevel:e.target.value}),className:"w-full px-3 py-1.25 border text-sm text-gray-500 border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,l.jsx)("option",{value:"L",children:"ต่ำ (L)"}),(0,l.jsx)("option",{value:"M",children:"ปานกลาง (M)"}),(0,l.jsx)("option",{value:"Q",children:"สูง (Q)"}),(0,l.jsx)("option",{value:"H",children:"สูงมาก (H)"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-500 mb-1",children:"ขนาด (px)"}),(0,l.jsx)("input",{type:"number",value:c.width,onChange:e=>x({...c,width:parseInt(e.target.value)||256}),min:"128",max:"1024",className:"w-full px-3 py-2 outline-none text-sm text-gray-500 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-500 mb-1",children:"สีเข้ม"}),(0,l.jsx)("input",{type:"color",value:c.color.dark,onChange:e=>x({...c,color:{...c.color,dark:e.target.value}}),className:"w-full h-10 border border-gray-300 rounded-md"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-500 mb-1",children:"สีอ่อน"}),(0,l.jsx)("input",{type:"color",value:c.color.light,onChange:e=>x({...c,color:{...c.color,light:e.target.value}}),className:"w-full h-10 border border-gray-300 rounded-md"})]})]})]})]}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,l.jsx)("button",{onClick:g,disabled:!e.trim()||a,className:"flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:a?"กำลังสร้าง...":"สร้าง QR Code"}),(0,l.jsx)("button",{onClick:()=>{r(""),s("")},className:"flex-1 bg-red-400 text-white py-3 px-6 rounded-lg font-medium hover:bg-red-500 transition-colors",children:"ล้างข้อมูล"})]})]}),(0,l.jsx)("div",{className:"flex flex-col items-center justify-center space-y-6",children:t?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow-md",children:(0,l.jsx)(i.default,{src:t,alt:"Generated QR Code",width:c.width,height:c.width,className:"max-w-full h-auto"})}),(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full",children:[(0,l.jsxs)("button",{onClick:()=>{if(!t)return;let e=document.createElement("a");e.download="qrcode-".concat(Date.now(),".png"),e.href=t,e.click()},className:"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center",children:[(0,l.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"ดาวน์โหลด"]}),(0,l.jsxs)("button",{onClick:p,className:"flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-center",children:[(0,l.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})}),"คัดลอก"]})]})]}):(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center h-64 text-gray-400",children:[(0,l.jsx)("svg",{className:"w-16 h-16 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,l.jsx)("p",{className:"text-lg font-medium",children:"QR Code จะแสดงที่นี่"}),(0,l.jsx)("p",{className:"text-sm",children:"ใส่ข้อความแล้วกดสร้าง QR Code"})]})})]}),(0,l.jsx)("canvas",{ref:h,style:{display:"none"}})]})}function c(){return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-b from-blue-200 to-white py-12 px-4 sm:px-6 lg:px-8 ".concat(a().className),children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsx)("h1",{className:"text-4xl text-blue-500 font-bold mb-4",children:"QR Code Generator"}),(0,l.jsx)("p",{className:"text-xl text-blue-500",children:"สร้าง QR Code ได้ง่ายๆ เพียงใส่ข้อความหรือ URL ที่ต้องการ"})]}),(0,l.jsx)(d,{})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[182,827,441,684,358],()=>r(883)),_N_E=e.O()}]);