'use client';

import { useState, useEffect } from 'react';

interface ExpirationData {
  content: string;
  expiresAt: string;
  message: string;
  createdAt: string;
}

interface ExpirationCheckerProps {
  qrData: string;
}

export default function ExpirationChecker({ qrData }: ExpirationCheckerProps) {
  const [isExpired, setIsExpired] = useState(false);
  const [expirationInfo, setExpirationInfo] = useState<ExpirationData | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<string>('');

  useEffect(() => {
    if (!qrData) return;

    try {
      // Check if it's a data URL with expiration info
      if (qrData.startsWith('data:application/json;base64,')) {
        const base64Data = qrData.split(',')[1];
        const decodedData = JSON.parse(atob(base64Data));
        setExpirationInfo(decodedData);
      } else {
        // Try to parse as JSON (for non-URL content)
        const parsedData = JSON.parse(qrData);
        if (parsedData.expiresAt) {
          setExpirationInfo(parsedData);
        }
      }
    } catch {
      // Not expiration-aware content, ignore
      setExpirationInfo(null);
    }
  }, [qrData]);

  useEffect(() => {
    if (!expirationInfo) return;

    const checkExpiration = () => {
      const now = new Date();
      const expiresAt = new Date(expirationInfo.expiresAt);
      const expired = now > expiresAt;
      
      setIsExpired(expired);

      if (!expired) {
        const timeDiff = expiresAt.getTime() - now.getTime();
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

        if (days > 0) {
          setTimeRemaining(`${days} วัน ${hours} ชั่วโมง`);
        } else if (hours > 0) {
          setTimeRemaining(`${hours} ชั่วโมง ${minutes} นาที`);
        } else if (minutes > 0) {
          setTimeRemaining(`${minutes} นาที ${seconds} วินาที`);
        } else {
          setTimeRemaining(`${seconds} วินาที`);
        }
      }
    };

    checkExpiration();
    const interval = setInterval(checkExpiration, 1000);

    return () => clearInterval(interval);
  }, [expirationInfo]);

  if (!expirationInfo) {
    return (
      <div className="text-xs text-green-600 bg-green-50 p-2 rounded">
        <span className="font-medium">✓ ใช้ได้ตลอด:</span> QR Code นี้ไม่มีการหมดอายุ
      </div>
    );
  }

  if (isExpired) {
    return (
      <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
        <div className="font-medium">❌ หมดอายุแล้ว</div>
        <div>หมดอายุเมื่อ: {new Date(expirationInfo.expiresAt).toLocaleString('th-TH')}</div>
        <div className="mt-1 text-red-500">{expirationInfo.message}</div>
      </div>
    );
  }

  return (
    <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded">
      <div className="font-medium">⏰ มีการหมดอายุ</div>
      <div>หมดอายุใน: {timeRemaining}</div>
      <div>หมดอายุเมื่อ: {new Date(expirationInfo.expiresAt).toLocaleString('th-TH')}</div>
    </div>
  );
}

// Utility function to extract original content from expiration-aware QR data
export function extractOriginalContent(qrData: string): string {
  try {
    // Check if it's a data URL with expiration info
    if (qrData.startsWith('data:application/json;base64,')) {
      const base64Data = qrData.split(',')[1];
      const decodedData = JSON.parse(atob(base64Data));
      return decodedData.content;
    }

    // Try to parse as JSON (for non-URL content)
    const parsedData = JSON.parse(qrData);
    if (parsedData.content) {
      return parsedData.content;
    }
  } catch {
    // Not expiration-aware content, return as is
  }
  
  return qrData;
}

// Utility function to check if content is expired
export function isContentExpired(qrData: string): boolean {
  try {
    let expirationData: ExpirationData | null = null;

    // Check if it's a data URL with expiration info
    if (qrData.startsWith('data:application/json;base64,')) {
      const base64Data = qrData.split(',')[1];
      expirationData = JSON.parse(atob(base64Data));
    } else {
      // Try to parse as JSON (for non-URL content)
      const parsedData = JSON.parse(qrData);
      if (parsedData.expiresAt) {
        expirationData = parsedData;
      }
    }

    if (expirationData) {
      const now = new Date();
      const expiresAt = new Date(expirationData.expiresAt);
      return now > expiresAt;
    }
  } catch {
    // Not expiration-aware content
  }
  
  return false;
}
