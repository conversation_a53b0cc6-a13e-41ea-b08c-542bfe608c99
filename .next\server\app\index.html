<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/e0f242f1e8d38a6a.css" data-precedence="next"/><link rel="stylesheet" href="/_next/static/css/67bfb09fd33343a4.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-078f7e6b036cfaf2.js"/><script src="/_next/static/chunks/4bd1b696-f02382649772bc48.js" async=""></script><script src="/_next/static/chunks/684-64d4b74a8af916f9.js" async=""></script><script src="/_next/static/chunks/main-app-79d0983040793bb6.js" async=""></script><script src="/_next/static/chunks/827-9f44e4e488803fe0.js" async=""></script><script src="/_next/static/chunks/app/page-c0f22e543d152fb7.js" async=""></script><title>QR Code Generator</title><meta name="description" content="QR Code Generator,สร้าง QR Code ได้ง่ายๆ เพียงใส่ข้อความ URL ที่ต้องการ, ต้องการสร้าง QR code"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_f8d785 __variable_406317 antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gradient-to-b from-blue-200 to-white py-12 px-4 sm:px-6 lg:px-8 __className_56b843"><div class="max-w-4xl mx-auto"><div class="text-center mb-12"><h1 class="text-4xl text-blue-500 font-bold mb-4">QR Code Generator</h1><p class="text-xl text-blue-500">สร้าง QR Code ได้ง่ายๆ เพียงใส่ข้อความหรือ URL ที่ต้องการ</p></div><div class="bg-white rounded-xl shadow-lg p-8"><div class="grid grid-cols-1 lg:grid-cols-2 gap-8"><div class="space-y-6"><div><label for="text-input" class="block text-lg font-bold text-gray-500 mb-2">ข้อความหรือ URL</label><textarea id="text-input" placeholder="ใส่ข้อความ, URL, หรือข้อมูลที่ต้องการสร้าง QR Code..." class="w-full h-32 px-4 py-3 outline-none text-gray-400 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none" maxLength="2000"></textarea><div class="text-right text-sm text-gray-500 mt-1">0<!-- -->/2000</div></div><div><button class="flex items-center text-md font-medium text-blue-400 hover:text-blue-500"><span>ตัวเลือกขั้นสูง</span><svg class="ml-1 h-4 w-4 transform transition-transform " fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg></button></div><div class="flex flex-col sm:flex-row gap-3"><button disabled="" class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors">สร้าง QR Code</button><button class="flex-1 bg-red-400 text-white py-3 px-6 rounded-lg font-medium hover:bg-red-500 transition-colors">ล้างข้อมูล</button></div></div><div class="flex flex-col items-center justify-center space-y-6"><div class="flex flex-col items-center justify-center h-64 text-gray-400"><svg class="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg><p class="text-lg font-medium">QR Code จะแสดงที่นี่</p><p class="text-sm">ใส่ข้อความแล้วกดสร้าง QR Code</p></div></div></div><canvas style="display:none"></canvas></div></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-078f7e6b036cfaf2.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[3022,[\"827\",\"static/chunks/827-9f44e4e488803fe0.js\",\"974\",\"static/chunks/app/page-c0f22e543d152fb7.js\"],\"default\"]\n8:I[9665,[],\"OutletBoundary\"]\nb:I[4911,[],\"AsyncMetadataOutlet\"]\nd:I[9665,[],\"ViewportBoundary\"]\nf:I[9665,[],\"MetadataBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/_next/static/css/e0f242f1e8d38a6a.css\",\"style\"]\n:HL[\"/_next/static/css/67bfb09fd33343a4.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"pTRnMOePClFeOA_M8OQue\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/e0f242f1e8d38a6a.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_f8d785 __variable_406317 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/67bfb09fd33343a4.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$L8\",null,{\"children\":[\"$L9\",\"$La\",[\"$\",\"$Lb\",null,{\"promise\":\"$@c\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"JIESDAdgs6cgSgOE6Nki9v\",{\"children\":[[\"$\",\"$Ld\",null,{\"children\":\"$Le\"}],null]}],[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n6:{}\n7:{}\n10:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]}]\n"])</script><script>self.__next_f.push([1,"a:null\n"])</script><script>self.__next_f.push([1,"e:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"c:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"QR Code Generator\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"QR Code Generator,สร้าง QR Code ได้ง่ายๆ เพียงใส่ข้อความ URL ที่ต้องการ, ต้องการสร้าง QR code\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n14:{\"metadata\":\"$c:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>