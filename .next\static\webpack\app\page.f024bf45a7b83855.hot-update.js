"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_font_google_target_css_path_src_app_page_tsx_import_IBM_Plex_Sans_Thai_arguments_weight_500_subsets_thai_variableName_ibmPlexSansThai___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\page.tsx\",\"import\":\"IBM_Plex_Sans_Thai\",\"arguments\":[{\"weight\":\"500\",\"subsets\":[\"thai\"]}],\"variableName\":\"ibmPlexSansThai\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\page.tsx\\\",\\\"import\\\":\\\"IBM_Plex_Sans_Thai\\\",\\\"arguments\\\":[{\\\"weight\\\":\\\"500\\\",\\\"subsets\\\":[\\\"thai\\\"]}],\\\"variableName\\\":\\\"ibmPlexSansThai\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_page_tsx_import_IBM_Plex_Sans_Thai_arguments_weight_500_subsets_thai_variableName_ibmPlexSansThai___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_page_tsx_import_IBM_Plex_Sans_Thai_arguments_weight_500_subsets_thai_variableName_ibmPlexSansThai___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_QRCodeGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/QRCodeGenerator */ \"(app-pages-browser)/./src/components/QRCodeGenerator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-blue-200 to-white py-12 px-4 sm:px-6 lg:px-8 \".concat((next_font_google_target_css_path_src_app_page_tsx_import_IBM_Plex_Sans_Thai_arguments_weight_500_subsets_thai_variableName_ibmPlexSansThai___WEBPACK_IMPORTED_MODULE_2___default().className)),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl text-blue-600 font-bold mb-4\",\n                            children: \"QR Code Generator\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-yellow-300\",\n                            children: \"สร้าง QR Code ได้ง่ายๆ เพียงใส่ข้อความหรือ URL ที่ต้องการ\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_QRCodeGenerator__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBS01BO0FBSHFEO0FBSzVDLFNBQVNFO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFXLG1GQUE2RyxPQUExQkosOExBQXlCO2tCQUMxSCw0RUFBQ0c7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQXdDOzs7Ozs7c0NBR3RELDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBMEI7Ozs7Ozs7Ozs7Ozs4QkFLekMsOERBQUNILG1FQUFlQTs7Ozs7Ozs7Ozs7Ozs7OztBQUl4QjtLQWpCd0JDIiwic291cmNlcyI6WyJIOlxcUFJPSkVDVFxcUVJjb2RlXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuaW1wb3J0IHsgSUJNX1BsZXhfU2Fuc19UaGFpIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBRUkNvZGVHZW5lcmF0b3IgZnJvbSAnQC9jb21wb25lbnRzL1FSQ29kZUdlbmVyYXRvcic7XG5cblxuY29uc3QgaWJtUGxleFNhbnNUaGFpID1JQk1fUGxleF9TYW5zX1RoYWkoeyB3ZWlnaHQ6IFwiNTAwXCIsIHN1YnNldHM6IFtcInRoYWlcIl0gfSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2BtaW4taC1zY3JlZW4gYmctZ3JhZGllbnQtdG8tYiBmcm9tLWJsdWUtMjAwIHRvLXdoaXRlIHB5LTEyIHB4LTQgc206cHgtNiBsZzpweC04ICR7aWJtUGxleFNhbnNUaGFpLmNsYXNzTmFtZX1gfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctNHhsIG14LWF1dG9cIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYi0xMlwiPlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCB0ZXh0LWJsdWUtNjAwIGZvbnQtYm9sZCBtYi00XCI+XG4gICAgICAgICAgICBRUiBDb2RlIEdlbmVyYXRvclxuICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LXllbGxvdy0zMDBcIj5cbiAgICAgICAgICAgIOC4quC4o+C5ieC4suC4hyBRUiBDb2RlIOC5hOC4lOC5ieC4h+C5iOC4suC4ouC5hiDguYDguJ7guLXguKLguIfguYPguKrguYjguILguYnguK3guITguKfguLLguKHguKvguKPguLfguK0gVVJMIOC4l+C4teC5iOC4leC5ieC4reC4h+C4geC4suC4o1xuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPFFSQ29kZUdlbmVyYXRvciAvPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaWJtUGxleFNhbnNUaGFpIiwiUVJDb2RlR2VuZXJhdG9yIiwiSG9tZSIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});