'use client';

import { useState, useRef } from 'react';
import QRCode from 'qrcode';
import Image from 'next/image';

interface QRCodeOptions {
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
  type: 'image/png' | 'image/jpeg' | 'image/webp';
  quality: number;
  margin: number;
  color: {
    dark: string;
    light: string;
  };
  width: number;
}

export default function QRCodeGenerator() {
  const [text, setText] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [options, setOptions] = useState<QRCodeOptions>({
    errorCorrectionLevel: 'M',
    type: 'image/png',
    quality: 0.92,
    margin: 1,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    },
    width: 256
  });
  const [showAdvanced, setShowAdvanced] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const generateQRCode = async () => {
    if (!text.trim()) return;
    
    setIsGenerating(true);
    try {
      const url = await QRCode.toDataURL(text, options);
      setQrCodeUrl(url);
      
      // Also generate to canvas for download functionality
      if (canvasRef.current) {
        await QRCode.toCanvas(canvasRef.current, text, options);
      }
    } catch (error) {
      console.error('Error generating QR code:', error);
      alert('เกิดข้อผิดพลาดในการสร้าง QR Code');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeUrl) return;
    
    const link = document.createElement('a');
    link.download = `qrcode-${Date.now()}.png`;
    link.href = qrCodeUrl;
    link.click();
  };

  const copyToClipboard = async () => {
    if (!qrCodeUrl) return;
    
    try {
      const response = await fetch(qrCodeUrl);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      alert('คัดลอก QR Code ไปยังคลิปบอร์ดแล้ว!');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      alert('ไม่สามารถคัดลอกได้ กรุณาใช้ปุ่มดาวน์โหลดแทน');
    }
  };

  const clearAll = () => {
    setText('');
    setQrCodeUrl('');
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          <div>
            <label htmlFor="text-input" className="block text-lg font-bold text-gray-500 mb-2">
              ข้อความหรือ URL
            </label>
            <textarea
              id="text-input"
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="ใส่ข้อความ, URL, หรือข้อมูลที่ต้องการสร้าง QR Code..."
              className="w-full h-32 px-4 py-3 outline-none text-gray-400 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              maxLength={2000}
            />
            <div className="text-right text-sm text-gray-500 mt-1">
              {text.length}/2000
            </div>
          </div>

          {/* Advanced Options */}
          <div>
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center text-md font-medium text-blue-400 hover:text-blue-500"
            >
              <span>ตัวเลือกขั้นสูง</span>
              <svg
                className={`ml-1 h-4 w-4 transform transition-transform ${showAdvanced ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {showAdvanced && (
              <div className="mt-4 space-y-4 p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      ระดับการแก้ไขข้อผิดพลาด
                    </label>
                    <select
                      value={options.errorCorrectionLevel}
                      onChange={(e) => setOptions({...options, errorCorrectionLevel: e.target.value as 'L' | 'M' | 'Q' | 'H'})}
                      className="w-full px-3 py-1.25 border text-sm text-gray-500 border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="L">ต่ำ (L)</option>
                      <option value="M">ปานกลาง (M)</option>
                      <option value="Q">สูง (Q)</option>
                      <option value="H">สูงมาก (H)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      ขนาด (px)
                    </label>
                    <input
                      type="number"
                      value={options.width}
                      onChange={(e) => setOptions({...options, width: parseInt(e.target.value) || 400})}
                      min="128"
                      max="1024"
                      className="w-full px-3 py-2 outline-none text-sm text-gray-500 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      สีเข้ม
                    </label>
                    <input
                      type="color"
                      value={options.color.dark}
                      onChange={(e) => setOptions({...options, color: {...options.color, dark: e.target.value}})}
                      className="w-full h-10 border border-gray-300 rounded-md"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">
                      สีอ่อน
                    </label>
                    <input
                      type="color"
                      value={options.color.light}
                      onChange={(e) => setOptions({...options, color: {...options.color, light: e.target.value}})}
                      className="w-full h-10 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <button
              onClick={generateQRCode}
              disabled={!text.trim() || isGenerating}
              className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isGenerating ? 'กำลังสร้าง...' : 'สร้าง QR Code'}
            </button>
            
            <button
              onClick={clearAll}
              className="flex-1 bg-red-400 text-white py-3 px-6 rounded-lg font-medium hover:bg-red-500 transition-colors"
            >
              ล้างข้อมูล
            </button>
          </div>
        </div>

        {/* QR Code Display Section */}
        <div className="flex flex-col items-center justify-center space-y-6">
          {qrCodeUrl ? (
            <>
              <div className="bg-white p-4 rounded-lg shadow-md">
                <Image
                  src={qrCodeUrl}
                  alt="Generated QR Code"
                  width={options.width}
                  height={options.width}
                  className="max-w-full h-auto"
                />
              </div>
              
              <div className="flex flex-col sm:flex-row gap-3 w-full">
                <button
                  onClick={downloadQRCode}
                  className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  ดาวน์โหลด
                </button>
                
                <button
                  onClick={copyToClipboard}
                  className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-center"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  คัดลอก
                </button>
              </div>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center h-64 text-gray-400">
              <svg className="w-16 h-16 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <p className="text-lg font-medium">QR Code จะแสดงที่นี่</p>
              <p className="text-sm">ใส่ข้อความแล้วกดสร้าง QR Code</p>
            </div>
          )}
        </div>
      </div>

      {/* Hidden canvas for download functionality */}
      <canvas ref={canvasRef} style={{ display: 'none' }} />
    </div>
  );
}
