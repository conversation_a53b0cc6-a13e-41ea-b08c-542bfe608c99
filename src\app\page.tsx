'use client';
import { IBM_Plex_Sans_Thai } from "next/font/google";
import QRCodeGenerator from '@/components/QRCodeGenerator';


const ibmPlexSansThai =IBM_Plex_Sans_Thai({ weight: "500", subsets: ["thai"] });

export default function Home() {
  return (
    <div className={`min-h-screen bg-gradient-to-b from-blue-200 to-white py-12 px-4 sm:px-6 lg:px-8 ${ibmPlexSansThai.className}`}>
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl text-blue-500 font-bold mb-4">
            QR Code Generator
          </h1>
          <p className="text-xl text-blue-500">
            สร้าง QR Code ได้ง่ายๆ เพียงใส่ข้อความหรือ URL ที่ต้องการ
          </p>
        </div>

        <QRCodeGenerator />
      </div>
    </div>
  );
}
