'use client';

import { useState } from 'react';
import { extractOriginalContent, isContentExpired } from './ExpirationChecker';

export default function QRScanner() {
  const [scannedData, setScannedData] = useState('');
  const [isExpired, setIsExpired] = useState(false);
  const [originalContent, setOriginalContent] = useState('');

  const handleScanInput = (data: string) => {
    setScannedData(data);
    
    // Check if content is expired
    const expired = isContentExpired(data);
    setIsExpired(expired);
    
    // Extract original content
    const content = extractOriginalContent(data);
    setOriginalContent(content);
  };

  const clearScan = () => {
    setScannedData('');
    setIsExpired(false);
    setOriginalContent('');
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mt-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-4">
        🔍 ทดสอบ QR Code Scanner
      </h2>
      
      <div className="space-y-4">
        <div>
          <label htmlFor="scan-input" className="block text-sm font-medium text-gray-700 mb-2">
            วาง QR Code Data ที่สแกนได้ (สำหรับทดสอบ)
          </label>
          <textarea
            id="scan-input"
            value={scannedData}
            onChange={(e) => handleScanInput(e.target.value)}
            placeholder="วาง QR Code data ที่สแกนได้ที่นี่..."
            className="w-full h-24 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
        </div>

        {scannedData && (
          <div className="space-y-3">
            {/* Expiration Status */}
            {isExpired ? (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <span className="font-medium text-red-800">QR Code นี้หมดอายุแล้ว</span>
                </div>
                <p className="text-red-700 mt-1">ไม่สามารถใช้งานได้</p>
              </div>
            ) : (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="font-medium text-green-800">QR Code ใช้งานได้</span>
                </div>
              </div>
            )}

            {/* Original Content */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">เนื้อหาต้นฉบับ:</h3>
              <div className="bg-white p-3 rounded border text-sm text-gray-700 break-all">
                {originalContent || 'ไม่พบเนื้อหา'}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
              {originalContent && originalContent.startsWith('http') && !isExpired && (
                <a
                  href={originalContent}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center"
                >
                  เปิดลิงก์
                </a>
              )}
              
              <button
                onClick={() => navigator.clipboard.writeText(originalContent)}
                className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors"
              >
                คัดลอกเนื้อหา
              </button>
              
              <button
                onClick={clearScan}
                className="bg-red-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-red-700 transition-colors"
              >
                ล้าง
              </button>
            </div>
          </div>
        )}

        {!scannedData && (
          <div className="text-center py-8 text-gray-500">
            <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
            </svg>
            <p className="text-lg font-medium">วาง QR Code Data เพื่อทดสอบ</p>
            <p className="text-sm">สามารถทดสอบ QR Code ที่มีการหมดอายุได้</p>
          </div>
        )}
      </div>
    </div>
  );
}
