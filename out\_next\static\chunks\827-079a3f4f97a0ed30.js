(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[827],{61:(e,t,r)=>{let n=r(1640);function o(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}o.prototype.initialize=function(e){this.degree=e,this.genPoly=n.generateECPolynomial(this.degree)},o.prototype.encode=function(e){if(!this.genPoly)throw Error("Encoder not initialized");let t=new Uint8Array(e.length+this.degree);t.set(e);let r=n.mod(t,this.genPoly),o=this.degree-r.length;if(o>0){let e=new Uint8Array(this.degree);return e.set(r,o),e}return r},e.exports=o},519:(e,t,r)=>{let n=r(8976),o=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],i=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];t.getBlocksCount=function(e,t){switch(t){case n.L:return o[(e-1)*4+0];case n.M:return o[(e-1)*4+1];case n.Q:return o[(e-1)*4+2];case n.H:return o[(e-1)*4+3];default:return}},t.getTotalCodewordsCount=function(e,t){switch(t){case n.L:return i[(e-1)*4+0];case n.M:return i[(e-1)*4+1];case n.Q:return i[(e-1)*4+2];case n.H:return i[(e-1)*4+3];default:return}}},645:(e,t,r)=>{let n=r(6087).getSymbolSize;t.getPositions=function(e){let t=n(e);return[[0,0],[t-7,0],[0,t-7]]}},901:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext(null)},908:(e,t)=>{function r(e){if("number"==typeof e&&(e=e.toString()),"string"!=typeof e)throw Error("Color should be defined as hex string");let t=e.slice().replace("#","").split("");if(t.length<3||5===t.length||t.length>8)throw Error("Invalid hex color: "+e);(3===t.length||4===t.length)&&(t=Array.prototype.concat.apply([],t.map(function(e){return[e,e]}))),6===t.length&&t.push("F","F");let r=parseInt(t.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+t.slice(0,6).join("")}}t.getOptions=function(e){e||(e={}),e.color||(e.color={});let t=void 0===e.margin||null===e.margin||e.margin<0?4:e.margin,n=e.width&&e.width>=21?e.width:void 0,o=e.scale||4;return{width:n,scale:n?4:o,margin:t,color:{dark:r(e.color.dark||"#000000ff"),light:r(e.color.light||"#ffffffff")},type:e.type,rendererOpts:e.rendererOpts||{}}},t.getScale=function(e,t){return t.width&&t.width>=e+2*t.margin?t.width/(e+2*t.margin):t.scale},t.getImageWidth=function(e,r){let n=t.getScale(e,r);return Math.floor((e+2*r.margin)*n)},t.qrToImageData=function(e,r,n){let o=r.modules.size,i=r.modules.data,a=t.getScale(o,n),l=Math.floor((o+2*n.margin)*a),u=n.margin*a,s=[n.color.light,n.color.dark];for(let t=0;t<l;t++)for(let r=0;r<l;r++){let d=(t*l+r)*4,f=n.color.light;t>=u&&r>=u&&t<l-u&&r<l-u&&(f=s[+!!i[Math.floor((t-u)/a)*o+Math.floor((r-u)/a)]]),e[d++]=f.r,e[d++]=f.g,e[d++]=f.b,e[d]=f.a}}},1193:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:o,quality:i}=e,a=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+o+"&q="+a+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},1469:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return u},getImageProps:function(){return l}});let n=r(8229),o=r(8883),i=r(3063),a=n._(r(1193));function l(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let u=i.Image},1640:(e,t,r)=>{let n=r(7354);t.mul=function(e,t){let r=new Uint8Array(e.length+t.length-1);for(let o=0;o<e.length;o++)for(let i=0;i<t.length;i++)r[o+i]^=n.mul(e[o],t[i]);return r},t.mod=function(e,t){let r=new Uint8Array(e);for(;r.length-t.length>=0;){let e=r[0];for(let o=0;o<t.length;o++)r[o]^=n.mul(t[o],e);let o=0;for(;o<r.length&&0===r[o];)o++;r=r.slice(o)}return r},t.generateECPolynomial=function(e){let r=new Uint8Array([1]);for(let o=0;o<e;o++)r=t.mul(r,new Uint8Array([1,n.exp(o)]));return r}},1685:(e,t)=>{let r="[0-9]+",n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",o="(?:(?![A-Z0-9 $%*+\\-./:]|"+(n=n.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";t.KANJI=RegExp(n,"g"),t.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),t.BYTE=RegExp(o,"g"),t.NUMERIC=RegExp(r,"g"),t.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let i=RegExp("^"+n+"$"),a=RegExp("^"+r+"$"),l=RegExp("^[A-Z0-9 $%*+\\-./:]+$");t.testKanji=function(e){return i.test(e)},t.testNumeric=function(e){return a.test(e)},t.testAlphanumeric=function(e){return l.test(e)}},2013:(e,t,r)=>{let n=r(908);function o(e,t){let r=e.a/255,n=t+'="'+e.hex+'"';return r<1?n+" "+t+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function i(e,t,r){let n=e+t;return void 0!==r&&(n+=" "+r),n}t.render=function(e,t,r){let a=n.getOptions(t),l=e.modules.size,u=e.modules.data,s=l+2*a.margin,d=a.color.light.a?"<path "+o(a.color.light,"fill")+' d="M0 0h'+s+"v"+s+'H0z"/>':"",f="<path "+o(a.color.dark,"stroke")+' d="'+function(e,t,r){let n="",o=0,a=!1,l=0;for(let u=0;u<e.length;u++){let s=Math.floor(u%t),d=Math.floor(u/t);s||a||(a=!0),e[u]?(l++,u>0&&s>0&&e[u-1]||(n+=a?i("M",s+r,.5+d+r):i("m",o,0),o=0,a=!1),s+1<t&&e[u+1]||(n+=i("h",l),l=0)):o++}return n}(u,l,a.margin)+'"/>',c='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+('viewBox="0 0 '+s+" ")+s+'" shape-rendering="crispEdges">'+d+f+"</svg>\n";return"function"==typeof r&&r(null,c),c}},2202:(e,t,r)=>{let n=r(6087),o=r(519),i=r(8976),a=r(3585),l=r(9343),u=n.getBCHDigit(7973);function s(e,t){return a.getCharCountIndicator(e,t)+4}t.from=function(e,t){return l.isValid(e)?parseInt(e,10):t},t.getCapacity=function(e,t,r){if(!l.isValid(e))throw Error("Invalid QR Code version");void 0===r&&(r=a.BYTE);let i=(n.getSymbolTotalCodewords(e)-o.getTotalCodewordsCount(e,t))*8;if(r===a.MIXED)return i;let u=i-s(r,e);switch(r){case a.NUMERIC:return Math.floor(u/10*3);case a.ALPHANUMERIC:return Math.floor(u/11*2);case a.KANJI:return Math.floor(u/13);case a.BYTE:default:return Math.floor(u/8)}},t.getBestVersionForData=function(e,r){let n,o=i.from(r,i.M);if(Array.isArray(e)){if(e.length>1){for(let r=1;r<=40;r++)if(function(e,t){let r=0;return e.forEach(function(e){let n=s(e.mode,t);r+=n+e.getBitsLength()}),r}(e,r)<=t.getCapacity(r,o,a.MIXED))return r;return}if(0===e.length)return 1;n=e[0]}else n=e;return function(e,r,n){for(let o=1;o<=40;o++)if(r<=t.getCapacity(o,n,e))return o}(n.mode,n.getLength(),o)},t.getEncodedBits=function(e){if(!l.isValid(e)||e<7)throw Error("Invalid QR Code version");let t=e<<12;for(;n.getBCHDigit(t)-u>=0;)t^=7973<<n.getBCHDigit(t)-u;return e<<12|t}},2464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=r(8229)._(r(2115)).default.createContext({})},2920:(e,t,r)=>{let n=r(3585),o=r(6444),i=r(9184),a=r(7487),l=r(5580),u=r(1685),s=r(6087),d=r(8521);function f(e){return unescape(encodeURIComponent(e)).length}function c(e,t,r){let n,o=[];for(;null!==(n=e.exec(r));)o.push({data:n[0],index:n.index,mode:t,length:n[0].length});return o}function g(e){let t,r,o=c(u.NUMERIC,n.NUMERIC,e),i=c(u.ALPHANUMERIC,n.ALPHANUMERIC,e);return s.isKanjiModeEnabled()?(t=c(u.BYTE,n.BYTE,e),r=c(u.KANJI,n.KANJI,e)):(t=c(u.BYTE_KANJI,n.BYTE,e),r=[]),o.concat(i,t,r).sort(function(e,t){return e.index-t.index}).map(function(e){return{data:e.data,mode:e.mode,length:e.length}})}function h(e,t){switch(t){case n.NUMERIC:return o.getBitsLength(e);case n.ALPHANUMERIC:return i.getBitsLength(e);case n.KANJI:return l.getBitsLength(e);case n.BYTE:return a.getBitsLength(e)}}function p(e,t){let r,u=n.getBestModeForData(e);if((r=n.from(t,u))!==n.BYTE&&r.bit<u.bit)throw Error('"'+e+'" cannot be encoded with mode '+n.toString(r)+".\n Suggested mode is: "+n.toString(u));switch(r===n.KANJI&&!s.isKanjiModeEnabled()&&(r=n.BYTE),r){case n.NUMERIC:return new o(e);case n.ALPHANUMERIC:return new i(e);case n.KANJI:return new l(e);case n.BYTE:return new a(e)}}t.fromArray=function(e){return e.reduce(function(e,t){return"string"==typeof t?e.push(p(t,null)):t.data&&e.push(p(t.data,t.mode)),e},[])},t.fromString=function(e,r){let o=function(e,t){let r={},o={start:{}},i=["start"];for(let a=0;a<e.length;a++){let l=e[a],u=[];for(let e=0;e<l.length;e++){let s=l[e],d=""+a+e;u.push(d),r[d]={node:s,lastCount:0},o[d]={};for(let e=0;e<i.length;e++){let a=i[e];r[a]&&r[a].node.mode===s.mode?(o[a][d]=h(r[a].lastCount+s.length,s.mode)-h(r[a].lastCount,s.mode),r[a].lastCount+=s.length):(r[a]&&(r[a].lastCount=s.length),o[a][d]=h(s.length,s.mode)+4+n.getCharCountIndicator(s.mode,t))}}i=u}for(let e=0;e<i.length;e++)o[i[e]].end=0;return{map:o,table:r}}(function(e){let t=[];for(let r=0;r<e.length;r++){let o=e[r];switch(o.mode){case n.NUMERIC:t.push([o,{data:o.data,mode:n.ALPHANUMERIC,length:o.length},{data:o.data,mode:n.BYTE,length:o.length}]);break;case n.ALPHANUMERIC:t.push([o,{data:o.data,mode:n.BYTE,length:o.length}]);break;case n.KANJI:t.push([o,{data:o.data,mode:n.BYTE,length:f(o.data)}]);break;case n.BYTE:t.push([{data:o.data,mode:n.BYTE,length:f(o.data)}])}}return t}(g(e,s.isKanjiModeEnabled())),r),i=d.find_path(o.map,"start","end"),a=[];for(let e=1;e<i.length-1;e++)a.push(o.table[i[e]].node);return t.fromArray(a.reduce(function(e,t){let r=e.length-1>=0?e[e.length-1]:null;return r&&r.mode===t.mode?e[e.length-1].data+=t.data:e.push(t),e},[]))},t.rawSplit=function(e){return t.fromArray(g(e,s.isKanjiModeEnabled()))}},3059:e=>{e.exports={style:{fontFamily:"'IBM Plex Sans Thai', 'IBM Plex Sans Thai Fallback'",fontWeight:500,fontStyle:"normal"},className:"__className_56b843"}},3063:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return w}});let n=r(8229),o=r(6966),i=r(5155),a=o._(r(2115)),l=n._(r(7650)),u=n._(r(5564)),s=r(8883),d=r(5840),f=r(6752);r(3230);let c=r(901),g=n._(r(1193)),h=r(6654),p={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function m(e,t,r,n,o,i,a){let l=null==e?void 0:e.src;e&&e["data-loaded-src"]!==l&&(e["data-loaded-src"]=l,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,o=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function y(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}let b=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:o,height:l,width:u,decoding:s,className:d,style:f,fetchPriority:c,placeholder:g,loading:p,unoptimized:b,fill:v,onLoadRef:w,onLoadingCompleteRef:E,setBlurComplete:C,setShowAltText:P,sizesInput:_,onLoad:M,onError:A,...I}=e,x=(0,a.useCallback)(e=>{e&&(A&&(e.src=e.src),e.complete&&m(e,g,w,E,C,b,_))},[r,g,w,E,C,A,b,_]),N=(0,h.useMergedRef)(t,x);return(0,i.jsx)("img",{...I,...y(c),loading:p,width:u,height:l,decoding:s,"data-nimg":v?"fill":"1",className:d,style:f,sizes:o,srcSet:n,src:r,ref:N,onLoad:e=>{m(e.currentTarget,g,w,E,C,b,_)},onError:e=>{P(!0),"empty"!==g&&C(!0),A&&A(e)}})});function v(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...y(r.fetchPriority)};return t&&l.default.preload?(l.default.preload(r.src,n),null):(0,i.jsx)(u.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let w=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(c.RouterContext),n=(0,a.useContext)(f.ImageConfigContext),o=(0,a.useMemo)(()=>{var e;let t=p||n||d.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:o,qualities:i}},[n]),{onLoad:l,onLoadingComplete:u}=e,h=(0,a.useRef)(l);(0,a.useEffect)(()=>{h.current=l},[l]);let m=(0,a.useRef)(u);(0,a.useEffect)(()=>{m.current=u},[u]);let[y,w]=(0,a.useState)(!1),[E,C]=(0,a.useState)(!1),{props:P,meta:_}=(0,s.getImgProps)(e,{defaultLoader:g.default,imgConf:o,blurComplete:y,showAltText:E});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b,{...P,unoptimized:_.unoptimized,placeholder:_.placeholder,fill:_.fill,onLoadRef:h,onLoadingCompleteRef:m,setBlurComplete:w,setShowAltText:C,sizesInput:e.sizes,ref:t}),_.priority?(0,i.jsx)(v,{isAppRouter:!r,imgAttributes:P}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3585:(e,t,r)=>{let n=r(9343),o=r(1685);t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(e,t){if(!e.ccBits)throw Error("Invalid mode: "+e);if(!n.isValid(t))throw Error("Invalid version: "+t);return t>=1&&t<10?e.ccBits[0]:t<27?e.ccBits[1]:e.ccBits[2]},t.getBestModeForData=function(e){return o.testNumeric(e)?t.NUMERIC:o.testAlphanumeric(e)?t.ALPHANUMERIC:o.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(e){if(e&&e.id)return e.id;throw Error("Invalid mode")},t.isValid=function(e){return e&&e.bit&&e.ccBits},t.from=function(e,r){if(t.isValid(e))return e;try{if("string"!=typeof e)throw Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw Error("Unknown mode: "+e)}}catch(e){return r}}},4122:e=>{function t(){this.buffer=[],this.length=0}t.prototype={get:function(e){let t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)==1},put:function(e,t){for(let r=0;r<t;r++)this.putBit((e>>>t-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(e){let t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},e.exports=t},4676:e=>{e.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},5029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(2115),o=n.useLayoutEffect,i=n.useEffect;function a(e){let{headManager:t,reduceComponentsToState:r}=e;function a(){if(t&&t.mountedInstances){let o=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(o,e))}}return o(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=a),()=>{t&&(t._pendingUpdate=a)})),i(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5100:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:o,blurDataURL:i,objectFit:a}=e,l=n?40*n:t,u=o?40*o:r,s=l&&u?"viewBox='0 0 "+l+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+s+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(s?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},5564:(e,t,r)=>{"use strict";var n=r(9509);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},defaultHead:function(){return c}});let o=r(8229),i=r(6966),a=r(5155),l=i._(r(2115)),u=o._(r(5029)),s=r(2464),d=r(2830),f=r(7544);function c(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function g(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===l.default.Fragment?e.concat(l.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(3230);let h=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(g,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let i=!0,a=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){a=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?i=!1:t.add(o.type);break;case"meta":for(let e=0,t=h.length;e<t;e++){let t=h[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!a)&&r.has(e)?i=!1:(r.add(e),n[t]=r)}}}return i}}()).reverse().map((e,t)=>{let o=e.key||t;if(n.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,l.default.cloneElement(e,t)}return l.default.cloneElement(e,{key:o})})}let m=function(e){let{children:t}=e,r=(0,l.useContext)(s.AmpStateContext),n=(0,l.useContext)(d.HeadManagerContext);return(0,a.jsx)(u.default,{reduceComponentsToState:p,headManager:n,inAmpMode:(0,f.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5580:(e,t,r)=>{let n=r(3585),o=r(6087);function i(e){this.mode=n.KANJI,this.data=e}i.getBitsLength=function(e){return 13*e},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let r=o.toSJIS(this.data[t]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw Error("Invalid SJIS character: "+this.data[t]+"\nMake sure your charset is UTF-8");r=(r>>>8&255)*192+(255&r),e.put(r,13)}},e.exports=i},5840:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},5908:(e,t,r)=>{let n=r(6087),o=r(8976),i=r(4122),a=r(9621),l=r(7788),u=r(645),s=r(6517),d=r(519),f=r(61),c=r(2202),g=r(8252),h=r(3585),p=r(2920);function m(e,t,r){let n,o,i=e.size,a=g.getEncodedBits(t,r);for(n=0;n<15;n++)o=(a>>n&1)==1,n<6?e.set(n,8,o,!0):n<8?e.set(n+1,8,o,!0):e.set(i-15+n,8,o,!0),n<8?e.set(8,i-n-1,o,!0):n<9?e.set(8,15-n-1+1,o,!0):e.set(8,15-n-1,o,!0);e.set(i-8,8,1,!0)}t.create=function(e,t){let r,g;if(void 0===e||""===e)throw Error("No input text");let y=o.M;return void 0!==t&&(y=o.from(t.errorCorrectionLevel,o.M),r=c.from(t.version),g=s.from(t.maskPattern),t.toSJISFunc&&n.setToSJISFunction(t.toSJISFunc)),function(e,t,r,o){let g;if(Array.isArray(e))g=p.fromArray(e);else if("string"==typeof e){let n=t;if(!n){let t=p.rawSplit(e);n=c.getBestVersionForData(t,r)}g=p.fromString(e,n||40)}else throw Error("Invalid data");let y=c.getBestVersionForData(g,r);if(!y)throw Error("The amount of data is too big to be stored in a QR Code");if(t){if(t<y)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+y+".\n")}else t=y;let b=function(e,t,r){let o=new i;r.forEach(function(t){o.put(t.mode.bit,4),o.put(t.getLength(),h.getCharCountIndicator(t.mode,e)),t.write(o)});let a=(n.getSymbolTotalCodewords(e)-d.getTotalCodewordsCount(e,t))*8;for(o.getLengthInBits()+4<=a&&o.put(0,4);o.getLengthInBits()%8!=0;)o.putBit(0);let l=(a-o.getLengthInBits())/8;for(let e=0;e<l;e++)o.put(e%2?17:236,8);return function(e,t,r){let o,i,a=n.getSymbolTotalCodewords(t),l=a-d.getTotalCodewordsCount(t,r),u=d.getBlocksCount(t,r),s=a%u,c=u-s,g=Math.floor(a/u),h=Math.floor(l/u),p=h+1,m=g-h,y=new f(m),b=0,v=Array(u),w=Array(u),E=0,C=new Uint8Array(e.buffer);for(let e=0;e<u;e++){let t=e<c?h:p;v[e]=C.slice(b,b+t),w[e]=y.encode(v[e]),b+=t,E=Math.max(E,t)}let P=new Uint8Array(a),_=0;for(o=0;o<E;o++)for(i=0;i<u;i++)o<v[i].length&&(P[_++]=v[i][o]);for(o=0;o<m;o++)for(i=0;i<u;i++)P[_++]=w[i][o];return P}(o,e,t)}(t,r,g),v=new a(n.getSymbolSize(t));!function(e,t){let r=e.size,n=u.getPositions(t);for(let t=0;t<n.length;t++){let o=n[t][0],i=n[t][1];for(let t=-1;t<=7;t++)if(!(o+t<=-1)&&!(r<=o+t))for(let n=-1;n<=7;n++)i+n<=-1||r<=i+n||(t>=0&&t<=6&&(0===n||6===n)||n>=0&&n<=6&&(0===t||6===t)||t>=2&&t<=4&&n>=2&&n<=4?e.set(o+t,i+n,!0,!0):e.set(o+t,i+n,!1,!0))}}(v,t);let w=v.size;for(let e=8;e<w-8;e++){let t=e%2==0;v.set(e,6,t,!0),v.set(6,e,t,!0)}return!function(e,t){let r=l.getPositions(t);for(let t=0;t<r.length;t++){let n=r[t][0],o=r[t][1];for(let t=-2;t<=2;t++)for(let r=-2;r<=2;r++)-2===t||2===t||-2===r||2===r||0===t&&0===r?e.set(n+t,o+r,!0,!0):e.set(n+t,o+r,!1,!0)}}(v,t),m(v,r,0),t>=7&&function(e,t){let r,n,o,i=e.size,a=c.getEncodedBits(t);for(let t=0;t<18;t++)r=Math.floor(t/3),n=t%3+i-8-3,o=(a>>t&1)==1,e.set(r,n,o,!0),e.set(n,r,o,!0)}(v,t),!function(e,t){let r=e.size,n=-1,o=r-1,i=7,a=0;for(let l=r-1;l>0;l-=2)for(6===l&&l--;;){for(let r=0;r<2;r++)if(!e.isReserved(o,l-r)){let n=!1;a<t.length&&(n=(t[a]>>>i&1)==1),e.set(o,l-r,n),-1==--i&&(a++,i=7)}if((o+=n)<0||r<=o){o-=n,n=-n;break}}}(v,b),isNaN(o)&&(o=s.getBestMask(v,m.bind(null,v,r))),s.applyMask(o,v),m(v,r,o),{modules:v,version:t,errorCorrectionLevel:r,maskPattern:o,segments:g}}(e,r,y,g)}},6087:(e,t)=>{let r,n=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];t.getSymbolSize=function(e){if(!e)throw Error('"version" cannot be null or undefined');if(e<1||e>40)throw Error('"version" should be in range from 1 to 40');return 4*e+17},t.getSymbolTotalCodewords=function(e){return n[e]},t.getBCHDigit=function(e){let t=0;for(;0!==e;)t++,e>>>=1;return t},t.setToSJISFunction=function(e){if("function"!=typeof e)throw Error('"toSJISFunc" is not a valid function.');r=e},t.isKanjiModeEnabled=function(){return void 0!==r},t.toSJIS=function(e){return r(e)}},6444:(e,t,r)=>{let n=r(3585);function o(e){this.mode=n.NUMERIC,this.data=e.toString()}o.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(e){let t,r,n;for(t=0;t+3<=this.data.length;t+=3)n=parseInt(this.data.substr(t,3),10),e.put(n,10);let o=this.data.length-t;o>0&&(n=parseInt(this.data.substr(t),10),e.put(n,3*o+1))},e.exports=o},6514:(e,t,r)=>{let n=r(908);t.render=function(e,t,r){var o;let i=r,a=t;void 0!==i||t&&t.getContext||(i=t,t=void 0),t||(a=function(){try{return document.createElement("canvas")}catch(e){throw Error("You need to specify a canvas element")}}()),i=n.getOptions(i);let l=n.getImageWidth(e.modules.size,i),u=a.getContext("2d"),s=u.createImageData(l,l);return n.qrToImageData(s.data,e,i),o=a,u.clearRect(0,0,o.width,o.height),o.style||(o.style={}),o.height=l,o.width=l,o.style.height=l+"px",o.style.width=l+"px",u.putImageData(s,0,0),a},t.renderToDataURL=function(e,r,n){let o=n;void 0!==o||r&&r.getContext||(o=r,r=void 0),o||(o={});let i=t.render(e,r,o),a=o.type||"image/png",l=o.rendererOpts||{};return i.toDataURL(a,l.quality)}},6517:(e,t)=>{t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let r={N1:3,N2:3,N3:40,N4:10};t.isValid=function(e){return null!=e&&""!==e&&!isNaN(e)&&e>=0&&e<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(e){let t=e.size,n=0,o=0,i=0,a=null,l=null;for(let u=0;u<t;u++){o=i=0,a=l=null;for(let s=0;s<t;s++){let t=e.get(u,s);t===a?o++:(o>=5&&(n+=r.N1+(o-5)),a=t,o=1),(t=e.get(s,u))===l?i++:(i>=5&&(n+=r.N1+(i-5)),l=t,i=1)}o>=5&&(n+=r.N1+(o-5)),i>=5&&(n+=r.N1+(i-5))}return n},t.getPenaltyN2=function(e){let t=e.size,n=0;for(let r=0;r<t-1;r++)for(let o=0;o<t-1;o++){let t=e.get(r,o)+e.get(r,o+1)+e.get(r+1,o)+e.get(r+1,o+1);(4===t||0===t)&&n++}return n*r.N2},t.getPenaltyN3=function(e){let t=e.size,n=0,o=0,i=0;for(let r=0;r<t;r++){o=i=0;for(let a=0;a<t;a++)o=o<<1&2047|e.get(r,a),a>=10&&(1488===o||93===o)&&n++,i=i<<1&2047|e.get(a,r),a>=10&&(1488===i||93===i)&&n++}return n*r.N3},t.getPenaltyN4=function(e){let t=0,n=e.data.length;for(let r=0;r<n;r++)t+=e.data[r];return Math.abs(Math.ceil(100*t/n/5)-10)*r.N4},t.applyMask=function(e,r){let n=r.size;for(let o=0;o<n;o++)for(let i=0;i<n;i++)r.isReserved(i,o)||r.xor(i,o,function(e,r,n){switch(e){case t.Patterns.PATTERN000:return(r+n)%2==0;case t.Patterns.PATTERN001:return r%2==0;case t.Patterns.PATTERN010:return n%3==0;case t.Patterns.PATTERN011:return(r+n)%3==0;case t.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(n/3))%2==0;case t.Patterns.PATTERN101:return r*n%2+r*n%3==0;case t.Patterns.PATTERN110:return(r*n%2+r*n%3)%2==0;case t.Patterns.PATTERN111:return(r*n%3+(r+n)%2)%2==0;default:throw Error("bad maskPattern:"+e)}}(e,i,o))},t.getBestMask=function(e,r){let n=Object.keys(t.Patterns).length,o=0,i=1/0;for(let a=0;a<n;a++){r(a),t.applyMask(a,e);let n=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(a,e),n<i&&(i=n,o=a)}return o}},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(2115);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=i(e,n)),t&&(o.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return i}});let n=r(8229)._(r(2115)),o=r(5840),i=n.default.createContext(o.imageConfigDefault)},6766:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(1469),o=r.n(n)},6862:(e,t,r)=>{let n=r(4676),o=r(5908),i=r(6514),a=r(2013);function l(e,t,r,i,a){let l=[].slice.call(arguments,1),u=l.length,s="function"==typeof l[u-1];if(!s&&!n())throw Error("Callback required as last argument");if(s){if(u<2)throw Error("Too few arguments provided");2===u?(a=r,r=t,t=i=void 0):3===u&&(t.getContext&&void 0===a?(a=i,i=void 0):(a=i,i=r,r=t,t=void 0))}else{if(u<1)throw Error("Too few arguments provided");return 1===u?(r=t,t=i=void 0):2!==u||t.getContext||(i=r,r=t,t=void 0),new Promise(function(n,a){try{let a=o.create(r,i);n(e(a,t,i))}catch(e){a(e)}})}try{let n=o.create(r,i);a(null,e(n,t,i))}catch(e){a(e)}}t.create=o.create,t.toCanvas=l.bind(null,i.render),t.toDataURL=l.bind(null,i.renderToDataURL),t.toString=l.bind(null,function(e,t,r){return a.render(e,r)})},7354:(e,t)=>{let r=new Uint8Array(512),n=new Uint8Array(256);!function(){let e=1;for(let t=0;t<255;t++)r[t]=e,n[e]=t,256&(e<<=1)&&(e^=285);for(let e=255;e<512;e++)r[e]=r[e-255]}(),t.log=function(e){if(e<1)throw Error("log("+e+")");return n[e]},t.exp=function(e){return r[e]},t.mul=function(e,t){return 0===e||0===t?0:r[n[e]+n[t]]}},7487:(e,t,r)=>{let n=r(3585);function o(e){this.mode=n.BYTE,"string"==typeof e?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}o.getBitsLength=function(e){return 8*e},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(e){for(let t=0,r=this.data.length;t<r;t++)e.put(this.data[t],8)},e.exports=o},7544:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},7788:(e,t,r)=>{let n=r(6087).getSymbolSize;t.getRowColCoords=function(e){if(1===e)return[];let t=Math.floor(e/7)+2,r=n(e),o=145===r?26:2*Math.ceil((r-13)/(2*t-2)),i=[r-7];for(let e=1;e<t-1;e++)i[e]=i[e-1]-o;return i.push(6),i.reverse()},t.getPositions=function(e){let r=[],n=t.getRowColCoords(e),o=n.length;for(let e=0;e<o;e++)for(let t=0;t<o;t++)(0!==e||0!==t)&&(0!==e||t!==o-1)&&(e!==o-1||0!==t)&&r.push([n[e],n[t]]);return r}},8252:(e,t,r)=>{let n=r(6087),o=n.getBCHDigit(1335);t.getEncodedBits=function(e,t){let r=e.bit<<3|t,i=r<<10;for(;n.getBCHDigit(i)-o>=0;)i^=1335<<n.getBCHDigit(i)-o;return(r<<10|i)^21522}},8521:e=>{"use strict";var t={single_source_shortest_paths:function(e,r,n){var o,i,a,l,u,s,d,f={},c={};c[r]=0;var g=t.PriorityQueue.make();for(g.push(r,0);!g.empty();)for(a in i=(o=g.pop()).value,l=o.cost,u=e[i]||{})u.hasOwnProperty(a)&&(s=l+u[a],d=c[a],(void 0===c[a]||d>s)&&(c[a]=s,g.push(a,s),f[a]=i));if(void 0!==n&&void 0===c[n])throw Error(["Could not find a path from ",r," to ",n,"."].join(""));return f},extract_shortest_path_from_predecessor_list:function(e,t){for(var r=[],n=t;n;)r.push(n),e[n],n=e[n];return r.reverse(),r},find_path:function(e,r,n){var o=t.single_source_shortest_paths(e,r,n);return t.extract_shortest_path_from_predecessor_list(o,n)},PriorityQueue:{make:function(e){var r,n=t.PriorityQueue,o={};for(r in e=e||{},n)n.hasOwnProperty(r)&&(o[r]=n[r]);return o.queue=[],o.sorter=e.sorter||n.default_sorter,o},default_sorter:function(e,t){return e.cost-t.cost},push:function(e,t){this.queue.push({value:e,cost:t}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};e.exports=t},8883:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return u}}),r(3230);let n=r(5100),o=r(5840),i=["-moz-initial","fill","none","scale-down",void 0];function a(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function u(e,t){var r,u;let s,d,f,{src:c,sizes:g,unoptimized:h=!1,priority:p=!1,loading:m,className:y,quality:b,width:v,height:w,fill:E=!1,style:C,overrideSrc:P,onLoad:_,onLoadingComplete:M,placeholder:A="empty",blurDataURL:I,fetchPriority:x,decoding:N="async",layout:R,objectFit:S,objectPosition:B,lazyBoundary:T,lazyRoot:j,...O}=e,{imgConf:L,showAltText:z,blurComplete:U,defaultLoader:k}=t,D=L||o.imageConfigDefault;if("allSizes"in D)s=D;else{let e=[...D.deviceSizes,...D.imageSizes].sort((e,t)=>e-t),t=D.deviceSizes.sort((e,t)=>e-t),n=null==(r=D.qualities)?void 0:r.sort((e,t)=>e-t);s={...D,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===k)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let F=O.loader||k;delete O.loader,delete O.srcSet;let H="__next_img_default"in F;if(H){if("custom"===s.loader)throw Object.defineProperty(Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=F;F=t=>{let{config:r,...n}=t;return e(n)}}if(R){"fill"===R&&(E=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(C={...C,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!g&&(g=t)}let J="",Y=l(v),K=l(w);if((u=c)&&"object"==typeof u&&(a(u)||void 0!==u.src)){let e=a(c)?c.default:c;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,f=e.blurHeight,I=I||e.blurDataURL,J=e.src,!E)if(Y||K){if(Y&&!K){let t=Y/e.width;K=Math.round(e.height*t)}else if(!Y&&K){let t=K/e.height;Y=Math.round(e.width*t)}}else Y=e.width,K=e.height}let V=!p&&("lazy"===m||void 0===m);(!(c="string"==typeof c?c:J)||c.startsWith("data:")||c.startsWith("blob:"))&&(h=!0,V=!1),s.unoptimized&&(h=!0),H&&!s.dangerouslyAllowSVG&&c.split("?",1)[0].endsWith(".svg")&&(h=!0);let q=l(b),Q=Object.assign(E?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:S,objectPosition:B}:{},z?{}:{color:"transparent"},C),W=U||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:Y,heightInt:K,blurWidth:d,blurHeight:f,blurDataURL:I||"",objectFit:Q.objectFit})+'")':'url("'+A+'")',$=i.includes(Q.objectFit)?"fill"===Q.objectFit?"100% 100%":"cover":Q.objectFit,G=W?{backgroundSize:$,backgroundPosition:Q.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:W}:{},X=function(e){let{config:t,src:r,unoptimized:n,width:o,quality:i,sizes:a,loader:l}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:u,kind:s}=function(e,t,r){let{deviceSizes:n,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,a),d=u.length-1;return{sizes:a||"w"!==s?a:"100vw",srcSet:u.map((e,n)=>l({config:t,src:r,quality:i,width:e})+" "+("w"===s?e:n+1)+s).join(", "),src:l({config:t,src:r,quality:i,width:u[d]})}}({config:s,src:c,unoptimized:h,width:Y,quality:q,sizes:g,loader:F});return{props:{...O,loading:V?"lazy":m,fetchPriority:x,width:Y,height:K,decoding:N,className:y,style:{...Q,...G},sizes:X.sizes,srcSet:X.srcSet,src:P||X.src},meta:{unoptimized:h,priority:p,placeholder:A,fill:E}}}},8976:(e,t)=>{t.L={bit:1},t.M={bit:0},t.Q={bit:3},t.H={bit:2},t.isValid=function(e){return e&&void 0!==e.bit&&e.bit>=0&&e.bit<4},t.from=function(e,r){if(t.isValid(e))return e;try{if("string"!=typeof e)throw Error("Param is not a string");switch(e.toLowerCase()){case"l":case"low":return t.L;case"m":case"medium":return t.M;case"q":case"quartile":return t.Q;case"h":case"high":return t.H;default:throw Error("Unknown EC Level: "+e)}}catch(e){return r}}},9184:(e,t,r)=>{let n=r(3585),o=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function i(e){this.mode=n.ALPHANUMERIC,this.data=e}i.getBitsLength=function(e){return 11*Math.floor(e/2)+e%2*6},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let r=45*o.indexOf(this.data[t]);r+=o.indexOf(this.data[t+1]),e.put(r,11)}this.data.length%2&&e.put(o.indexOf(this.data[t]),6)},e.exports=i},9343:(e,t)=>{t.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40}},9621:e=>{function t(e){if(!e||e<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}t.prototype.set=function(e,t,r,n){let o=e*this.size+t;this.data[o]=r,n&&(this.reservedBit[o]=!0)},t.prototype.get=function(e,t){return this.data[e*this.size+t]},t.prototype.xor=function(e,t,r){this.data[e*this.size+t]^=r},t.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]},e.exports=t}}]);