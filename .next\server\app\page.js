(()=>{var e={};e.id=974,e.ids=[974],e.modules={53:(e,t)=>{let r="\x1b[37m",n="\x1b[30m",i="\x1b[0m",o="\x1b[47m"+n,s="\x1b[40m"+r,a=function(e,t,r,n){let i=t+1;return r>=i||n>=i||n<-1||r<-1?"0":r>=t||n>=t||n<0||r<0?"1":e[n*t+r]?"2":"1"},l=function(e,t,r,n){return a(e,t,r,n)+a(e,t,r,n+1)};t.render=function(e,t,a){var u,c;let f=e.modules.size,h=e.modules.data,d=!!(t&&t.inverse),p=t&&t.inverse?s:o,g={"00":i+" "+p,"01":i+(u=d?n:r)+"▄"+p,"02":i+(c=d?r:n)+"▄"+p,10:i+u+"▀"+p,11:" ",12:"▄",20:i+c+"▀"+p,21:"▀",22:"█"},m=i+"\n"+p,_=p;for(let e=-1;e<f+1;e+=2){for(let t=-1;t<f;t++)_+=g[l(h,f,t,e)];_+=g[l(h,f,f,e)]+m}return _+=i,"function"==typeof a&&a(null,_),_}},284:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.ImageConfigContext},300:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var n=r(687),i=r(1269),o=r.n(i),s=r(3210),a=r(2516),l=r(1261),u=r.n(l);function c(){let[e,t]=(0,s.useState)(""),[r,i]=(0,s.useState)(""),[o,l]=(0,s.useState)(!1),[c,f]=(0,s.useState)({errorCorrectionLevel:"M",type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"},width:256}),[h,d]=(0,s.useState)(!1),p=(0,s.useRef)(null),g=async()=>{if(e.trim()){l(!0);try{let t=await a.toDataURL(e,c);i(t),p.current&&await a.toCanvas(p.current,e,c)}catch(e){console.error("Error generating QR code:",e),alert("เกิดข้อผิดพลาดในการสร้าง QR Code")}finally{l(!1)}}},m=async()=>{if(r)try{let e=await fetch(r),t=await e.blob();await navigator.clipboard.write([new ClipboardItem({"image/png":t})]),alert("คัดลอก QR Code ไปยังคลิปบอร์ดแล้ว!")}catch(e){console.error("Error copying to clipboard:",e),alert("ไม่สามารถคัดลอกได้ กรุณาใช้ปุ่มดาวน์โหลดแทน")}};return(0,n.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"text-input",className:"block text-lg font-bold text-gray-500 mb-2",children:"ข้อความหรือ URL"}),(0,n.jsx)("textarea",{id:"text-input",value:e,onChange:e=>t(e.target.value),placeholder:"ใส่ข้อความ, URL, หรือข้อมูลที่ต้องการสร้าง QR Code...",className:"w-full h-32 px-4 py-3 outline-none text-gray-400 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",maxLength:2e3}),(0,n.jsxs)("div",{className:"text-right text-sm text-gray-500 mt-1",children:[e.length,"/2000"]})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("button",{onClick:()=>d(!h),className:"flex items-center text-md font-medium text-blue-400 hover:text-blue-500",children:[(0,n.jsx)("span",{children:"ตัวเลือกขั้นสูง"}),(0,n.jsx)("svg",{className:`ml-1 h-4 w-4 transform transition-transform ${h?"rotate-180":""}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),h&&(0,n.jsxs)("div",{className:"mt-4 space-y-4 p-4 bg-gray-50 rounded-lg",children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-500 mb-1",children:"ระดับการแก้ไขข้อผิดพลาด"}),(0,n.jsxs)("select",{value:c.errorCorrectionLevel,onChange:e=>f({...c,errorCorrectionLevel:e.target.value}),className:"w-full px-3 py-1.25 border text-sm text-gray-500 border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,n.jsx)("option",{value:"L",children:"ต่ำ (L)"}),(0,n.jsx)("option",{value:"M",children:"ปานกลาง (M)"}),(0,n.jsx)("option",{value:"Q",children:"สูง (Q)"}),(0,n.jsx)("option",{value:"H",children:"สูงมาก (H)"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-500 mb-1",children:"ขนาด (px)"}),(0,n.jsx)("input",{type:"number",value:c.width,onChange:e=>f({...c,width:parseInt(e.target.value)||256}),min:"128",max:"1024",className:"w-full px-3 py-2 outline-none text-sm text-gray-500 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-500 mb-1",children:"สีเข้ม"}),(0,n.jsx)("input",{type:"color",value:c.color.dark,onChange:e=>f({...c,color:{...c.color,dark:e.target.value}}),className:"w-full h-10 border border-gray-300 rounded-md"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-500 mb-1",children:"สีอ่อน"}),(0,n.jsx)("input",{type:"color",value:c.color.light,onChange:e=>f({...c,color:{...c.color,light:e.target.value}}),className:"w-full h-10 border border-gray-300 rounded-md"})]})]})]})]}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,n.jsx)("button",{onClick:g,disabled:!e.trim()||o,className:"flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:o?"กำลังสร้าง...":"สร้าง QR Code"}),(0,n.jsx)("button",{onClick:()=>{t(""),i("")},className:"flex-1 bg-red-400 text-white py-3 px-6 rounded-lg font-medium hover:bg-red-500 transition-colors",children:"ล้างข้อมูล"})]})]}),(0,n.jsx)("div",{className:"flex flex-col items-center justify-center space-y-6",children:r?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"bg-white p-4 rounded-lg shadow-md",children:(0,n.jsx)(u(),{src:r,alt:"Generated QR Code",width:c.width,height:c.width,className:"max-w-full h-auto"})}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 w-full",children:[(0,n.jsxs)("button",{onClick:()=>{if(!r)return;let e=document.createElement("a");e.download=`qrcode-${Date.now()}.png`,e.href=r,e.click()},className:"flex-1 bg-green-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"ดาวน์โหลด"]}),(0,n.jsxs)("button",{onClick:m,className:"flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors flex items-center justify-center",children:[(0,n.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})}),"คัดลอก"]})]})]}):(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center h-64 text-gray-400",children:[(0,n.jsx)("svg",{className:"w-16 h-16 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),(0,n.jsx)("p",{className:"text-lg font-medium",children:"QR Code จะแสดงที่นี่"}),(0,n.jsx)("p",{className:"text-sm",children:"ใส่ข้อความแล้วกดสร้าง QR Code"})]})})]}),(0,n.jsx)("canvas",{ref:p,style:{display:"none"}})]})}function f(){return(0,n.jsx)("div",{className:`min-h-screen bg-gradient-to-b from-blue-200 to-white py-12 px-4 sm:px-6 lg:px-8 ${o().className}`,children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsxs)("div",{className:"text-center mb-12",children:[(0,n.jsx)("h1",{className:"text-4xl text-blue-500 font-bold mb-4",children:"QR Code Generator"}),(0,n.jsx)("p",{className:"text-xl text-blue-500",children:"สร้าง QR Code ได้ง่ายๆ เพียงใส่ข้อความหรือ URL ที่ต้องการ"})]}),(0,n.jsx)(c,{})]})})}},395:(e,t,r)=>{let n=r(3751),i=r(4089);t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(e,t){if(!e.ccBits)throw Error("Invalid mode: "+e);if(!n.isValid(t))throw Error("Invalid version: "+t);return t>=1&&t<10?e.ccBits[0]:t<27?e.ccBits[1]:e.ccBits[2]},t.getBestModeForData=function(e){return i.testNumeric(e)?t.NUMERIC:i.testAlphanumeric(e)?t.ALPHANUMERIC:i.testKanji(e)?t.KANJI:t.BYTE},t.toString=function(e){if(e&&e.id)return e.id;throw Error("Invalid mode")},t.isValid=function(e){return e&&e.bit&&e.ccBits},t.from=function(e,r){if(t.isValid(e))return e;try{if("string"!=typeof e)throw Error("Param is not a string");switch(e.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw Error("Unknown mode: "+e)}}catch(e){return r}}},431:(e,t,r)=>{"use strict";let n=r(4967);e.exports=function(e,t,r,i){let o=-1!==[n.COLORTYPE_COLOR_ALPHA,n.COLORTYPE_ALPHA].indexOf(i.colorType);if(i.colorType===i.inputColorType){let t,r=(new DataView(t=new ArrayBuffer(2)).setInt16(0,256,!0),256!==new Int16Array(t)[0]);if(8===i.bitDepth||16===i.bitDepth&&r)return e}let s=16!==i.bitDepth?e:new Uint16Array(e.buffer),a=255,l=n.COLORTYPE_TO_BPP_MAP[i.inputColorType];4!==l||i.inputHasAlpha||(l=3);let u=n.COLORTYPE_TO_BPP_MAP[i.colorType];16===i.bitDepth&&(a=65535,u*=2);let c=Buffer.alloc(t*r*u),f=0,h=0,d=i.bgColor||{};void 0===d.red&&(d.red=a),void 0===d.green&&(d.green=a),void 0===d.blue&&(d.blue=a);for(let e=0;e<r;e++)for(let e=0;e<t;e++){let e=function(){let e,t,r,l=a;switch(i.inputColorType){case n.COLORTYPE_COLOR_ALPHA:l=s[f+3],e=s[f],t=s[f+1],r=s[f+2];break;case n.COLORTYPE_COLOR:e=s[f],t=s[f+1],r=s[f+2];break;case n.COLORTYPE_ALPHA:l=s[f+1],t=e=s[f],r=e;break;case n.COLORTYPE_GRAYSCALE:t=e=s[f],r=e;break;default:throw Error("input color type:"+i.inputColorType+" is not supported at present")}return i.inputHasAlpha&&!o&&(l/=a,e=Math.min(Math.max(Math.round((1-l)*d.red+l*e),0),a),t=Math.min(Math.max(Math.round((1-l)*d.green+l*t),0),a),r=Math.min(Math.max(Math.round((1-l)*d.blue+l*r),0),a)),{red:e,green:t,blue:r,alpha:l}}(s,f);switch(i.colorType){case n.COLORTYPE_COLOR_ALPHA:case n.COLORTYPE_COLOR:8===i.bitDepth?(c[h]=e.red,c[h+1]=e.green,c[h+2]=e.blue,o&&(c[h+3]=e.alpha)):(c.writeUInt16BE(e.red,h),c.writeUInt16BE(e.green,h+2),c.writeUInt16BE(e.blue,h+4),o&&c.writeUInt16BE(e.alpha,h+6));break;case n.COLORTYPE_ALPHA:case n.COLORTYPE_GRAYSCALE:{let t=(e.red+e.green+e.blue)/3;8===i.bitDepth?(c[h]=t,o&&(c[h+1]=e.alpha)):(c.writeUInt16BE(t,h),o&&c.writeUInt16BE(e.alpha,h+2));break}default:throw Error("unrecognised color Type "+i.colorType)}f+=l,h+=u}return c}},440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},512:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},defaultHead:function(){return f}});let n=r(4985),i=r(740),o=r(687),s=i._(r(3210)),a=n._(r(7755)),l=r(4959),u=r(9513),c=r(4604);function f(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function h(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===s.default.Fragment?e.concat(s.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(148);let d=["name","httpEquiv","charSet","itemProp"];function p(e,t){let{inAmpMode:r}=t;return e.reduce(h,[]).reverse().concat(f(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return i=>{let o=!0,s=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){s=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=d.length;e<t;e++){let t=d[e];if(i.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?o=!1:r.add(t);else{let e=i.props[t],r=n[t]||new Set;("name"!==t||!s)&&r.has(e)?o=!1:(r.add(e),n[t]=r)}}}return o}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,s.default.cloneElement(e,t)}return s.default.cloneElement(e,{key:n})})}let g=function(e){let{children:t}=e,r=(0,s.useContext)(l.AmpStateContext),n=(0,s.useContext)(u.HeadManagerContext);return(0,o.jsx)(a.default,{reduceComponentsToState:p,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},524:(e,t)=>{let r=new Uint8Array(512),n=new Uint8Array(256);!function(){let e=1;for(let t=0;t<255;t++)r[t]=e,n[e]=t,256&(e<<=1)&&(e^=285);for(let e=255;e<512;e++)r[e]=r[e-255]}(),t.log=function(e){if(e<1)throw Error("log("+e+")");return n[e]},t.exp=function(e){return r[e]},t.mul=function(e,t){return 0===e||0===t?0:r[n[e]+n[t]]}},539:(e,t,r)=>{"use strict";let n=r(8354),i=r(7910),o=e.exports=function(){i.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};n.inherits(o,i),o.prototype.read=function(e,t){this._reads.push({length:Math.abs(e),allowLess:e<0,func:t}),process.nextTick((function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}).bind(this))},o.prototype.write=function(e,t){let r;return this.writable?(r=Buffer.isBuffer(e)?e:Buffer.from(e,t||this._encoding),this._buffers.push(r),this._buffered+=r.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused):(this.emit("error",Error("Stream not writable")),!1)},o.prototype.end=function(e,t){e&&this.write(e,t),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},o.prototype.destroySoon=o.prototype.end,o.prototype._end=function(){this._reads.length>0&&this.emit("error",Error("Unexpected end of input")),this.destroy()},o.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},o.prototype._processReadAllowingLess=function(e){this._reads.shift();let t=this._buffers[0];t.length>e.length?(this._buffered-=e.length,this._buffers[0]=t.slice(e.length),e.func.call(this,t.slice(0,e.length))):(this._buffered-=t.length,this._buffers.shift(),e.func.call(this,t))},o.prototype._processRead=function(e){this._reads.shift();let t=0,r=0,n=Buffer.alloc(e.length);for(;t<e.length;){let i=this._buffers[r++],o=Math.min(i.length,e.length-t);i.copy(n,t,0,o),t+=o,o!==i.length&&(this._buffers[--r]=i.slice(o))}r>0&&this._buffers.splice(0,r),this._buffered-=e.length,e.func.call(this,n)},o.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let e=this._reads[0];if(e.allowLess)this._processReadAllowingLess(e);else if(this._buffered>=e.length)this._processRead(e);else break}this._buffers&&!this.writable&&this._end()}catch(e){this.emit("error",e)}}},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},816:(e,t,r)=>{let n=r(9227),i=n.getBCHDigit(1335);t.getEncodedBits=function(e,t){let r=e.bit<<3|t,o=r<<10;for(;n.getBCHDigit(o)-i>=0;)o^=1335<<n.getBCHDigit(o)-i;return(r<<10|o)^21522}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"H:\\\\PROJECT\\\\QRcode\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"H:\\PROJECT\\QRcode\\src\\app\\page.tsx","default")},1220:(e,t,r)=>{"use strict";let n=!0,i=r(4075);i.deflateSync||(n=!1);let o=r(4967),s=r(8358);e.exports=function(e,t){if(!n)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let r=new s(t||{}),a=[];a.push(Buffer.from(o.PNG_SIGNATURE)),a.push(r.packIHDR(e.width,e.height)),e.gamma&&a.push(r.packGAMA(e.gamma));let l=r.filterData(e.data,e.width,e.height),u=i.deflateSync(l,r.getDeflateOptions());if(l=null,!u||!u.length)throw Error("bad png - invalid compressed data response");return a.push(r.packIDAT(u)),a.push(r.packIEND()),Buffer.concat(a)}},1261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return a}});let n=r(4985),i=r(4953),o=r(6533),s=n._(r(1933));function a(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=o.Image},1269:e=>{e.exports={style:{fontFamily:"'IBM Plex Sans Thai', 'IBM Plex Sans Thai Fallback'",fontWeight:500,fontStyle:"normal"},className:"__className_56b843"}},1414:(e,t,r)=>{let n=r(395),i=r(9090),o=r(1952),s=r(4395),a=r(2342),l=r(4089),u=r(9227),c=r(7233);function f(e){return unescape(encodeURIComponent(e)).length}function h(e,t,r){let n,i=[];for(;null!==(n=e.exec(r));)i.push({data:n[0],index:n.index,mode:t,length:n[0].length});return i}function d(e){let t,r,i=h(l.NUMERIC,n.NUMERIC,e),o=h(l.ALPHANUMERIC,n.ALPHANUMERIC,e);return u.isKanjiModeEnabled()?(t=h(l.BYTE,n.BYTE,e),r=h(l.KANJI,n.KANJI,e)):(t=h(l.BYTE_KANJI,n.BYTE,e),r=[]),i.concat(o,t,r).sort(function(e,t){return e.index-t.index}).map(function(e){return{data:e.data,mode:e.mode,length:e.length}})}function p(e,t){switch(t){case n.NUMERIC:return i.getBitsLength(e);case n.ALPHANUMERIC:return o.getBitsLength(e);case n.KANJI:return a.getBitsLength(e);case n.BYTE:return s.getBitsLength(e)}}function g(e,t){let r,l=n.getBestModeForData(e);if((r=n.from(t,l))!==n.BYTE&&r.bit<l.bit)throw Error('"'+e+'" cannot be encoded with mode '+n.toString(r)+".\n Suggested mode is: "+n.toString(l));switch(r===n.KANJI&&!u.isKanjiModeEnabled()&&(r=n.BYTE),r){case n.NUMERIC:return new i(e);case n.ALPHANUMERIC:return new o(e);case n.KANJI:return new a(e);case n.BYTE:return new s(e)}}t.fromArray=function(e){return e.reduce(function(e,t){return"string"==typeof t?e.push(g(t,null)):t.data&&e.push(g(t.data,t.mode)),e},[])},t.fromString=function(e,r){let i=function(e,t){let r={},i={start:{}},o=["start"];for(let s=0;s<e.length;s++){let a=e[s],l=[];for(let e=0;e<a.length;e++){let u=a[e],c=""+s+e;l.push(c),r[c]={node:u,lastCount:0},i[c]={};for(let e=0;e<o.length;e++){let s=o[e];r[s]&&r[s].node.mode===u.mode?(i[s][c]=p(r[s].lastCount+u.length,u.mode)-p(r[s].lastCount,u.mode),r[s].lastCount+=u.length):(r[s]&&(r[s].lastCount=u.length),i[s][c]=p(u.length,u.mode)+4+n.getCharCountIndicator(u.mode,t))}}o=l}for(let e=0;e<o.length;e++)i[o[e]].end=0;return{map:i,table:r}}(function(e){let t=[];for(let r=0;r<e.length;r++){let i=e[r];switch(i.mode){case n.NUMERIC:t.push([i,{data:i.data,mode:n.ALPHANUMERIC,length:i.length},{data:i.data,mode:n.BYTE,length:i.length}]);break;case n.ALPHANUMERIC:t.push([i,{data:i.data,mode:n.BYTE,length:i.length}]);break;case n.KANJI:t.push([i,{data:i.data,mode:n.BYTE,length:f(i.data)}]);break;case n.BYTE:t.push([{data:i.data,mode:n.BYTE,length:f(i.data)}])}}return t}(d(e,u.isKanjiModeEnabled())),r),o=c.find_path(i.map,"start","end"),s=[];for(let e=1;e<o.length-1;e++)s.push(i.table[o[e]].node);return t.fromArray(s.reduce(function(e,t){let r=e.length-1>=0?e[e.length-1]:null;return r&&r.mode===t.mode?e[e.length-1].data+=t.data:e.push(t),e},[]))},t.rawSplit=function(e){return t.fromArray(d(e,u.isKanjiModeEnabled()))}},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return o}});let n=r(4722),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function s(e){let t,r,o;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,o]=e.split(r,2);break}if(!t||!r||!o)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":o="/"===t?"/"+o:t+"/"+o;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});o=s.slice(0,-2).concat(o).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:o}}},1480:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:o,objectFit:s}=e,a=n?40*n:t,l=i?40*i:r,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},1653:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return h},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return d}});let n=r(8304),i=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),o=r(6341),s=r(4396),a=r(660),l=r(4722),u=r(2958),c=r(5499);function f(e){let t=i.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(r=(0,a.djb2Hash)(t).toString(36).slice(0,6)),r}function h(e,t,r){let n=(0,l.normalizeAppPath)(e),a=(0,s.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),c=(0,o.interpolateDynamicPath)(n,t,a),{name:h,ext:d}=i.default.parse(r),p=f(i.default.posix.join(e,h)),g=p?`-${p}`:"";return(0,u.normalizePathSep)(i.default.join(c,`${h}${g}${d}`))}function d(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=f(e),!t.endsWith("/route")){let{dir:e,name:n,ext:o}=i.default.parse(t);t=i.default.posix.join(e,`${n}${r?`-${r}`:""}${o}`,"route")}return t}function p(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,i=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${i}`)+(r?"/route":"")}},1667:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>f,pages:()=>c,routeModule:()=>h,tree:()=>u});var n=r(5239),i=r(8088),o=r(8170),s=r.n(o),a=r(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"H:\\PROJECT\\QRcode\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"H:\\PROJECT\\QRcode\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["H:\\PROJECT\\QRcode\\src\\app\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},1933:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:i,quality:o}=e,s=o||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+i+"&q="+s+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},1952:(e,t,r)=>{let n=r(395),i=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function o(e){this.mode=n.ALPHANUMERIC,this.data=e}o.getBitsLength=function(e){return 11*Math.floor(e/2)+e%2*6},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(e){let t;for(t=0;t+2<=this.data.length;t+=2){let r=45*i.indexOf(this.data[t]);r+=i.indexOf(this.data[t+1]),e.put(r,11)}this.data.length%2&&e.put(i.indexOf(this.data[t]),6)},e.exports=o},2e3:e=>{"use strict";e.exports=function(e,t){let r=t.depth,n=t.width,i=t.height,o=t.colorType,s=t.transColor,a=t.palette,l=e;return 3===o?!function(e,t,r,n,i){let o=0;for(let s=0;s<n;s++)for(let n=0;n<r;n++){let r=i[e[o]];if(!r)throw Error("index "+e[o]+" not in palette");for(let e=0;e<4;e++)t[o+e]=r[e];o+=4}}(e,l,n,i,a):(s&&function(e,t,r,n,i){let o=0;for(let s=0;s<n;s++)for(let n=0;n<r;n++){let r=!1;if(1===i.length?i[0]===e[o]&&(r=!0):i[0]===e[o]&&i[1]===e[o+1]&&i[2]===e[o+2]&&(r=!0),r)for(let e=0;e<4;e++)t[o+e]=0;o+=4}}(e,l,n,i,s),8!==r&&(16===r&&(l=Buffer.alloc(n*i*4)),!function(e,t,r,n,i){let o=Math.pow(2,i)-1,s=0;for(let i=0;i<n;i++)for(let n=0;n<r;n++){for(let r=0;r<4;r++)t[s+r]=Math.floor(255*e[s+r]/o+.5);s+=4}}(e,l,n,i,r))),l}},2213:e=>{"use strict";e.exports=function(e,t,r){let n=e+t-r,i=Math.abs(n-e),o=Math.abs(n-t),s=Math.abs(n-r);return i<=o&&i<=s?e:o<=s?t:r}},2214:(e,t,r)=>{"use strict";let n=r(2213),i={0:function(e,t,r,n,i){for(let o=0;o<r;o++)n[i+o]=e[t+o]},1:function(e,t,r,n,i,o){for(let s=0;s<r;s++){let r=s>=o?e[t+s-o]:0,a=e[t+s]-r;n[i+s]=a}},2:function(e,t,r,n,i){for(let o=0;o<r;o++){let s=t>0?e[t+o-r]:0,a=e[t+o]-s;n[i+o]=a}},3:function(e,t,r,n,i,o){for(let s=0;s<r;s++){let a=s>=o?e[t+s-o]:0,l=t>0?e[t+s-r]:0,u=e[t+s]-(a+l>>1);n[i+s]=u}},4:function(e,t,r,i,o,s){for(let a=0;a<r;a++){let l=a>=s?e[t+a-s]:0,u=t>0?e[t+a-r]:0,c=t>0&&a>=s?e[t+a-(r+s)]:0,f=e[t+a]-n(l,u,c);i[o+a]=f}}},o={0:function(e,t,r){let n=0,i=t+r;for(let r=t;r<i;r++)n+=Math.abs(e[r]);return n},1:function(e,t,r,n){let i=0;for(let o=0;o<r;o++){let r=o>=n?e[t+o-n]:0;i+=Math.abs(e[t+o]-r)}return i},2:function(e,t,r){let n=0,i=t+r;for(let o=t;o<i;o++){let i=t>0?e[o-r]:0;n+=Math.abs(e[o]-i)}return n},3:function(e,t,r,n){let i=0;for(let o=0;o<r;o++){let s=o>=n?e[t+o-n]:0,a=t>0?e[t+o-r]:0;i+=Math.abs(e[t+o]-(s+a>>1))}return i},4:function(e,t,r,i){let o=0;for(let s=0;s<r;s++){let a=s>=i?e[t+s-i]:0,l=t>0?e[t+s-r]:0,u=t>0&&s>=i?e[t+s-(r+i)]:0;o+=Math.abs(e[t+s]-n(a,l,u))}return o}};e.exports=function(e,t,r,n,s){let a;if("filterType"in n&&-1!==n.filterType)if("number"==typeof n.filterType)a=[n.filterType];else throw Error("unrecognised filter types");else a=[0,1,2,3,4];16===n.bitDepth&&(s*=2);let l=t*s,u=0,c=0,f=Buffer.alloc((l+1)*r),h=a[0];for(let t=0;t<r;t++){if(a.length>1){let t=1/0;for(let r=0;r<a.length;r++){let n=o[a[r]](e,c,l,s);n<t&&(h=a[r],t=n)}}f[u]=h,u++,i[h](e,c,l,f,u,s),u+=l,c+=l}return f}},2251:(e,t,r)=>{let n=r(9227).getSymbolSize;t.getPositions=function(e){let t=n(e);return[[0,0],[t-7,0],[0,t-7]]}},2342:(e,t,r)=>{let n=r(395),i=r(9227);function o(e){this.mode=n.KANJI,this.data=e}o.getBitsLength=function(e){return 13*e},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(e){let t;for(t=0;t<this.data.length;t++){let r=i.toSJIS(this.data[t]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw Error("Invalid SJIS character: "+this.data[t]+"\nMake sure your charset is UTF-8");r=(r>>>8&255)*192+(255&r),e.put(r,13)}},e.exports=o},2412:e=>{"use strict";e.exports=require("assert")},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(5362);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),o=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=o(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},2511:e=>{"use strict";let t=e.exports=function(e){this._buffer=e,this._reads=[]};t.prototype.read=function(e,t){this._reads.push({length:Math.abs(e),allowLess:e<0,func:t})},t.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let e=this._reads[0];if(this._buffer.length&&(this._buffer.length>=e.length||e.allowLess)){this._reads.shift();let t=this._buffer;this._buffer=t.slice(e.length),e.func.call(this,t.slice(0,e.length))}else break}return this._reads.length>0?Error("There are some read requests waitng on finished stream"):this._buffer.length>0?Error("unrecognised content at end of stream"):void 0}},2516:(e,t,r)=>{"use strict";e.exports=r(3177)},2756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(r,n(e));else t.set(r,n(i));return t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},2792:(e,t,r)=>{let n=r(9815),i=r(53);t.render=function(e,t,r){return t&&t.small?i.render(e,t,r):n.render(e,t,r)}},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let n=r(3210);function i(e,t){let r=(0,n.useRef)(null),i=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(r.current=o(e,n)),t&&(i.current=o(t,n))},[e,t])}function o(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3177:(e,t,r)=>{let n=r(9642),i=r(8082),o=r(8653),s=r(4763),a=r(2792),l=r(8808);function u(e,t,r){if(void 0===e)throw Error("String required as first argument");if(void 0===r&&(r=t,t={}),"function"!=typeof r)if(n())t=r||{},r=null;else throw Error("Callback required as last argument");return{opts:t,cb:r}}function c(e){switch(e){case"svg":return l;case"txt":case"utf8":return s;default:return o}}function f(e,t,r){if(!r.cb)return new Promise(function(n,o){try{let s=i.create(t,r.opts);return e(s,r.opts,function(e,t){return e?o(e):n(t)})}catch(e){o(e)}});try{let n=i.create(t,r.opts);return e(n,r.opts,r.cb)}catch(e){r.cb(e)}}t.create=i.create,t.toCanvas=r(5032).toCanvas,t.toString=function(e,t,r){let n=u(e,t,r);return f(function(e){switch(e){case"svg":return l;case"terminal":return a;default:return s}}(n.opts?n.opts.type:void 0).render,e,n)},t.toDataURL=function(e,t,r){let n=u(e,t,r);return f(c(n.opts.type).renderToDataURL,e,n)},t.toBuffer=function(e,t,r){let n=u(e,t,r);return f(c(n.opts.type).renderToBuffer,e,n)},t.toFile=function(e,t,r,i){if("string"!=typeof e||"string"!=typeof t&&"object"!=typeof t)throw Error("Invalid argument");if(arguments.length<3&&!n())throw Error("Too few arguments provided");let o=u(t,r,i);return f(c(o.opts.type||e.slice((e.lastIndexOf(".")-1>>>0)+2).toLowerCase()).renderToFile.bind(null,e),t,o)},t.toFileStream=function(e,t,r){if(arguments.length<2)throw Error("Too few arguments provided");let n=u(t,r,e.emit.bind(e,"error"));f(c("png").renderToFileStream.bind(null,e),t,n)}},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(4827);let n=r(2785);function i(e,t,r){void 0===r&&(r=!0);let i=new URL("http://n"),o=t?new URL(t,i):e.startsWith(".")?new URL("http://n"):i,{pathname:s,searchParams:a,search:l,hash:u,href:c,origin:f}=new URL(e,o);if(f!==i.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:r?(0,n.searchParamsToUrlQuery)(a):void 0,search:l,hash:u,href:c.slice(f.length)}}},3751:(e,t)=>{t.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40}},3873:e=>{"use strict";e.exports=require("path")},3876:()=>{},4075:e=>{"use strict";e.exports=require("zlib")},4089:(e,t)=>{let r="[0-9]+",n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",i="(?:(?![A-Z0-9 $%*+\\-./:]|"+(n=n.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";t.KANJI=RegExp(n,"g"),t.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),t.BYTE=RegExp(i,"g"),t.NUMERIC=RegExp(r,"g"),t.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let o=RegExp("^"+n+"$"),s=RegExp("^"+r+"$"),a=RegExp("^[A-Z0-9 $%*+\\-./:]+$");t.testKanji=function(e){return o.test(e)},t.testNumeric=function(e){return s.test(e)},t.testAlphanumeric=function(e){return a.test(e)}},4196:()=>{},4395:(e,t,r)=>{let n=r(395);function i(e){this.mode=n.BYTE,"string"==typeof e?this.data=new TextEncoder().encode(e):this.data=new Uint8Array(e)}i.getBitsLength=function(e){return 8*e},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(e){for(let t=0,r=this.data.length;t<r;t++)e.put(this.data[t],8)},e.exports=i},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return g},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return f},parseParameter:function(){return l}});let n=r(6143),i=r(1437),o=r(3293),s=r(2887),a=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(a);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let n={},l=1,c=[];for(let f of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.find(e=>f.startsWith(e)),s=f.match(a);if(e&&s&&s[2]){let{key:t,optional:r,repeat:i}=u(s[2]);n[t]={pos:l++,repeat:i,optional:r},c.push("/"+(0,o.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:i}=u(s[2]);n[e]={pos:l++,repeat:t,optional:i},r&&s[1]&&c.push("/"+(0,o.escapeStringRegexp)(s[1]));let a=t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&s[1]&&(a=a.substring(1)),c.push(a)}else c.push("/"+(0,o.escapeStringRegexp)(f));t&&s&&s[3]&&c.push((0,o.escapeStringRegexp)(s[3]))}return{parameterizedRoute:c.join(""),groups:n}}function f(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:i=!1}=void 0===t?{}:t,{parameterizedRoute:o,groups:s}=c(e,r,n),a=o;return i||(a+="(?:/)?"),{re:RegExp("^"+a+"$"),groups:s}}function h(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:i,routeKeys:s,keyPrefix:a,backreferenceDuplicateKeys:l}=e,{key:c,optional:f,repeat:h}=u(i),d=c.replace(/\W/g,"");a&&(d=""+a+d);let p=!1;(0===d.length||d.length>30)&&(p=!0),isNaN(parseInt(d.slice(0,1)))||(p=!0),p&&(d=n());let g=d in s;a?s[d]=""+a+c:s[d]=c;let m=r?(0,o.escapeStringRegexp)(r):"";return t=g&&l?"\\k<"+d+">":h?"(?<"+d+">.+?)":"(?<"+d+">[^/]+?)",f?"(?:/"+m+t+")?":"/"+m+t}function d(e,t,r,l,u){let c,f=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),d={},p=[];for(let c of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=i.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),s=c.match(a);if(e&&s&&s[2])p.push(h({getSafeRouteKey:f,interceptionMarker:s[1],segment:s[2],routeKeys:d,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(s&&s[2]){l&&s[1]&&p.push("/"+(0,o.escapeStringRegexp)(s[1]));let e=h({getSafeRouteKey:f,segment:s[2],routeKeys:d,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&s[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,o.escapeStringRegexp)(c));r&&s&&s[3]&&p.push((0,o.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:d}}function p(e,t){var r,n,i;let o=d(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(i=t.backreferenceDuplicateKeys)&&i),s=o.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...f(e,t),namedRegex:"^"+s+"$",routeKeys:o.routeKeys}}function g(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=d(e,!1,!1,!1,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>l});var n=r(7413),i=r(8711),o=r.n(i),s=r(5465),a=r.n(s);r(1135);let l={title:"QR Code Generator",description:"QR Code Generator,สร้าง QR Code ได้ง่ายๆ เพียงใส่ข้อความ URL ที่ต้องการ, ต้องการสร้าง QR code"};function u({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${o().variable} ${a().variable} antialiased`,children:e})})}},4581:(e,t,r)=>{"use strict";let n=r(2511),i=r(9108);t.process=function(e,t){let r=[],o=new n(e);return new i(t,{read:o.read.bind(o),write:function(e){r.push(e)},complete:function(){}}).start(),o.process(),Buffer.concat(r)}},4604:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},4632:(e,t,r)=>{let n=r(9483);t.render=function(e,t,r){var i;let o=r,s=t;void 0!==o||t&&t.getContext||(o=t,t=void 0),t||(s=function(){try{return document.createElement("canvas")}catch(e){throw Error("You need to specify a canvas element")}}()),o=n.getOptions(o);let a=n.getImageWidth(e.modules.size,o),l=s.getContext("2d"),u=l.createImageData(a,a);return n.qrToImageData(u.data,e,o),i=s,l.clearRect(0,0,i.width,i.height),i.style||(i.style={}),i.height=a,i.width=a,i.style.height=a+"px",i.style.width=a+"px",l.putImageData(u,0,0),s},t.renderToDataURL=function(e,r,n){let i=n;void 0!==i||r&&r.getContext||(i=r,r=void 0),i||(i={});let o=t.render(e,r,i),s=i.type||"image/png",a=i.rendererOpts||{};return o.toDataURL(s,a.quality)}},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return s}});let n=r(5531),i=r(5499);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},4763:(e,t,r)=>{let n=r(9483),i={WW:" ",WB:"▄",BB:"█",BW:"▀"},o={BB:" ",BW:"▄",WW:"█",WB:"▀"};t.render=function(e,t,r){let s=n.getOptions(t),a=i;("#ffffff"===s.color.dark.hex||"#000000"===s.color.light.hex)&&(a=o);let l=e.modules.size,u=e.modules.data,c="",f=Array(l+2*s.margin+1).join(a.WW);f=Array(s.margin/2+1).join(f+"\n");let h=Array(s.margin+1).join(a.WW);c+=f;for(let e=0;e<l;e+=2){c+=h;for(let t=0;t<l;t++){var d;let r=u[e*l+t],n=u[(e+1)*l+t];c+=(d=a,r&&n?d.BB:r&&!n?d.BW:!r&&n?d.WB:d.WW)}c+=h+"\n"}return c+=f.slice(0,-1),"function"==typeof r&&r(null,c),c},t.renderToFile=function(e,n,i,o){void 0===o&&(o=i,i=void 0);let s=r(9021),a=t.render(n,i);s.writeFile(e,a,o)}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return _},NormalizeError:function(){return g},PageNotFoundError:function(){return m},SP:function(){return h},ST:function(){return d},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return a},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function a(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let h="undefined"!=typeof performance,d=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class g extends Error{}class m extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class _ extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},4868:(e,t,r)=>{let n=r(9227).getSymbolSize;t.getRowColCoords=function(e){if(1===e)return[];let t=Math.floor(e/7)+2,r=n(e),i=145===r?26:2*Math.ceil((r-13)/(2*t-2)),o=[r-7];for(let e=1;e<t-1;e++)o[e]=o[e-1]-i;return o.push(6),o.reverse()},t.getPositions=function(e){let r=[],n=t.getRowColCoords(e),i=n.length;for(let e=0;e<i;e++)for(let t=0;t<i;t++)(0!==e||0!==t)&&(0!==e||t!==i-1)&&(e!==i-1||0!==t)&&r.push([n[e],n[t]]);return r}},4902:(e,t,r)=>{let n=r(524);t.mul=function(e,t){let r=new Uint8Array(e.length+t.length-1);for(let i=0;i<e.length;i++)for(let o=0;o<t.length;o++)r[i+o]^=n.mul(e[i],t[o]);return r},t.mod=function(e,t){let r=new Uint8Array(e);for(;r.length-t.length>=0;){let e=r[0];for(let i=0;i<t.length;i++)r[i]^=n.mul(t[i],e);let i=0;for(;i<r.length&&0===r[i];)i++;r=r.slice(i)}return r},t.generateECPolynomial=function(e){let r=new Uint8Array([1]);for(let i=0;i<e;i++)r=t.mul(r,new Uint8Array([1,n.exp(i)]));return r}},4953:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(148);let n=r(1480),i=r(2756),o=["-moz-initial","fill","none","scale-down",void 0];function s(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let u,c,f,{src:h,sizes:d,unoptimized:p=!1,priority:g=!1,loading:m,className:_,quality:y,width:b,height:E,fill:x=!1,style:v,overrideSrc:w,onLoad:R,onLoadingComplete:P,placeholder:C="empty",blurDataURL:T,fetchPriority:A,decoding:O="async",layout:I,objectFit:M,objectPosition:N,lazyBoundary:S,lazyRoot:j,...k}=e,{imgConf:L,showAltText:B,blurComplete:D,defaultLoader:U}=t,F=L||i.imageConfigDefault;if("allSizes"in F)u=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),n=null==(r=F.qualities)?void 0:r.sort((e,t)=>e-t);u={...F,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===U)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let z=k.loader||U;delete k.loader,delete k.srcSet;let H="__next_img_default"in z;if(H){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+h+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=z;z=t=>{let{config:r,...n}=t;return e(n)}}if(I){"fill"===I&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[I];e&&(v={...v,...e});let t={responsive:"100vw",fill:"100vw"}[I];t&&!d&&(d=t)}let Y="",$=a(b),q=a(E);if((l=h)&&"object"==typeof l&&(s(l)||void 0!==l.src)){let e=s(h)?h.default:h;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,f=e.blurHeight,T=T||e.blurDataURL,Y=e.src,!x)if($||q){if($&&!q){let t=$/e.width;q=Math.round(e.height*t)}else if(!$&&q){let t=q/e.height;$=Math.round(e.width*t)}}else $=e.width,q=e.height}let G=!g&&("lazy"===m||void 0===m);(!(h="string"==typeof h?h:Y)||h.startsWith("data:")||h.startsWith("blob:"))&&(p=!0,G=!1),u.unoptimized&&(p=!0),H&&!u.dangerouslyAllowSVG&&h.split("?",1)[0].endsWith(".svg")&&(p=!0);let W=a(y),Q=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:N}:{},B?{}:{color:"transparent"},v),K=D||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:$,heightInt:q,blurWidth:c,blurHeight:f,blurDataURL:T||"",objectFit:Q.objectFit})+'")':'url("'+C+'")',V=o.includes(Q.objectFit)?"fill"===Q.objectFit?"100% 100%":"cover":Q.objectFit,X=K?{backgroundSize:V,backgroundPosition:Q.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:K}:{},J=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:o,sizes:s,loader:a}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,s),c=l.length-1;return{sizes:s||"w"!==u?s:"100vw",srcSet:l.map((e,n)=>a({config:t,src:r,quality:o,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:a({config:t,src:r,quality:o,width:l[c]})}}({config:u,src:h,unoptimized:p,width:$,quality:W,sizes:d,loader:z});return{props:{...k,loading:G?"lazy":m,fetchPriority:A,width:$,height:q,decoding:O,className:_,style:{...Q,...X},sizes:J.sizes,srcSet:J.srcSet,src:w||J.src},meta:{unoptimized:p,priority:g,placeholder:C,fill:x}}}},4959:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.AmpContext},4967:e=>{"use strict";e.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:0x49484452,TYPE_IEND:0x49454e44,TYPE_IDAT:0x49444154,TYPE_PLTE:0x504c5445,TYPE_tRNS:0x74524e53,TYPE_gAMA:0x67414d41,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},5032:(e,t,r)=>{let n=r(9642),i=r(8082),o=r(4632),s=r(6701);function a(e,t,r,o,s){let a=[].slice.call(arguments,1),l=a.length,u="function"==typeof a[l-1];if(!u&&!n())throw Error("Callback required as last argument");if(u){if(l<2)throw Error("Too few arguments provided");2===l?(s=r,r=t,t=o=void 0):3===l&&(t.getContext&&void 0===s?(s=o,o=void 0):(s=o,o=r,r=t,t=void 0))}else{if(l<1)throw Error("Too few arguments provided");return 1===l?(r=t,t=o=void 0):2!==l||t.getContext||(o=r,r=t,t=void 0),new Promise(function(n,s){try{let s=i.create(r,o);n(e(s,t,o))}catch(e){s(e)}})}try{let n=i.create(r,o);s(null,e(n,t,o))}catch(e){s(e)}}i.create,t.toCanvas=a.bind(null,o.render),a.bind(null,o.renderToDataURL),a.bind(null,function(e,t,r){return s.render(e,r)})},5221:(e,t,r)=>{Promise.resolve().then(r.bind(r,300))},5299:(e,t)=>{"use strict";let r=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];t.getImagePasses=function(e,t){let n=[],i=e%8,o=t%8,s=(e-i)/8,a=(t-o)/8;for(let e=0;e<r.length;e++){let t=r[e],l=s*t.x.length,u=a*t.y.length;for(let e=0;e<t.x.length;e++)if(t.x[e]<i)l++;else break;for(let e=0;e<t.y.length;e++)if(t.y[e]<o)u++;else break;l>0&&u>0&&n.push({width:l,height:u,index:e})}return n},t.getInterlaceIterator=function(e){return function(t,n,i){let o=t%r[i].x.length,s=(t-o)/r[i].x.length*8+r[i].x[o],a=n%r[i].y.length;return 4*s+((n-a)/r[i].y.length*8+r[i].y[a])*e*4}}},5329:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",o=r+1;o<e.length;){var s=e.charCodeAt(o);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){i+=e[o++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=o;continue}if("("===n){var a=1,l="",o=r+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '+o);for(;o<e.length;){if("\\"===e[o]){l+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--a){o++;break}}else if("("===e[o]&&(a++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at "+o);l+=e[o++]}if(a)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=o;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,o=void 0===n?"./":n,s="[^"+i(t.delimiter||"/#?")+"]+?",a=[],l=0,u=0,c="",f=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=f(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},d=function(){for(var e,t="";e=f("CHAR")||f("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var p=f("CHAR"),g=f("NAME"),m=f("PATTERN");if(g||m){var _=p||"";-1===o.indexOf(_)&&(c+=_,_=""),c&&(a.push(c),c=""),a.push({name:g||l++,prefix:_,suffix:"",pattern:m||s,modifier:f("MODIFIER")||""});continue}var y=p||f("ESCAPED_CHAR");if(y){c+=y;continue}if(c&&(a.push(c),c=""),f("OPEN")){var _=d(),b=f("NAME")||"",E=f("PATTERN")||"",x=d();h("CLOSE"),a.push({name:b||(E?l++:""),pattern:b&&!E?s:E,prefix:_,suffix:x,modifier:f("MODIFIER")||""});continue}h("END")}return a}function r(e,t){void 0===t&&(t={});var r=o(t),n=t.encode,i=void 0===n?function(e){return e}:n,s=t.validate,a=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var o=e[n];if("string"==typeof o){r+=o;continue}var s=t?t[o.name]:void 0,u="?"===o.modifier||"*"===o.modifier,c="*"===o.modifier||"+"===o.modifier;if(Array.isArray(s)){if(!c)throw TypeError('Expected "'+o.name+'" to not repeat, but got an array');if(0===s.length){if(u)continue;throw TypeError('Expected "'+o.name+'" to not be empty')}for(var f=0;f<s.length;f++){var h=i(s[f],o);if(a&&!l[n].test(h))throw TypeError('Expected all "'+o.name+'" to match "'+o.pattern+'", but got "'+h+'"');r+=o.prefix+h+o.suffix}continue}if("string"==typeof s||"number"==typeof s){var h=i(String(s),o);if(a&&!l[n].test(h))throw TypeError('Expected "'+o.name+'" to match "'+o.pattern+'", but got "'+h+'"');r+=o.prefix+h+o.suffix;continue}if(!u){var d=c?"an array":"a string";throw TypeError('Expected "'+o.name+'" to be '+d)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var o=n[0],s=n.index,a=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?a[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):a[r.name]=i(n[e],r)}}(l);return{path:o,index:s,params:a}}}function i(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function o(e){return e&&e.sensitive?"":"i"}function s(e,t,r){void 0===r&&(r={});for(var n=r.strict,s=void 0!==n&&n,a=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,f="["+i(r.endsWith||"")+"]|$",h="["+i(r.delimiter||"/#?")+"]",d=void 0===a||a?"^":"",p=0;p<e.length;p++){var g=e[p];if("string"==typeof g)d+=i(c(g));else{var m=i(c(g.prefix)),_=i(c(g.suffix));if(g.pattern)if(t&&t.push(g),m||_)if("+"===g.modifier||"*"===g.modifier){var y="*"===g.modifier?"?":"";d+="(?:"+m+"((?:"+g.pattern+")(?:"+_+m+"(?:"+g.pattern+"))*)"+_+")"+y}else d+="(?:"+m+"("+g.pattern+")"+_+")"+g.modifier;else d+="("+g.pattern+")"+g.modifier;else d+="(?:"+m+_+")"+g.modifier}}if(void 0===l||l)s||(d+=h+"?"),d+=r.endsWith?"(?="+f+")":"$";else{var b=e[e.length-1],E="string"==typeof b?h.indexOf(b[b.length-1])>-1:void 0===b;s||(d+="(?:"+h+"(?="+f+"))?"),E||(d+="(?="+h+"|"+f+")")}return new RegExp(d,o(r))}function a(t,r,n){if(t instanceof RegExp){if(!r)return t;var i=t.source.match(/\((?!\?)/g);if(i)for(var l=0;l<i.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return a(e,r,n).source}).join("|")+")",o(n)):s(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(a(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=s,t.pathToRegexp=a})(),e.exports=t})()},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return f},prepareDestination:function(){return h}});let n=r(5362),i=r(3293),o=r(6759),s=r(1437),a=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},o=r=>{let n,o=r.key;switch(r.type){case"header":o=o.toLowerCase(),n=e.headers[o];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,a.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[o];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(o)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!(!r.every(e=>o(e))||n.some(e=>o(e)))&&i}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function f(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,i.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,o.parseUrl)(t),n=r.pathname;n&&(n=l(n));let s=r.href;s&&(s=l(s));let a=r.hostname;a&&(a=l(a));let u=r.hash;return u&&(u=l(u)),{...r,pathname:n,hostname:a,href:s,hash:u}}function h(e){let t,r,i=Object.assign({},e.query),o=f(e),{hostname:a,query:u}=o,h=o.pathname;o.hash&&(h=""+h+o.hash);let d=[],p=[];for(let e of((0,n.pathToRegexp)(h,p),p))d.push(e.name);if(a){let e=[];for(let t of((0,n.pathToRegexp)(a,e),e))d.push(t.name)}let g=(0,n.compile)(h,{validate:!1});for(let[r,i]of(a&&(t=(0,n.compile)(a,{validate:!1})),Object.entries(u)))Array.isArray(i)?u[r]=i.map(t=>c(l(t),e.params)):"string"==typeof i&&(u[r]=c(l(i),e.params));let m=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!m.some(e=>d.includes(e)))for(let t of m)t in u||(u[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let r=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,i]=(r=g(e.params)).split("#",2);t&&(o.hostname=t(e.params)),o.pathname=n,o.hash=(i?"#":"")+(i||""),delete o.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return o.query={...i,...o.query},{newUrl:r,destQuery:u,parsedDestination:o}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},5868:e=>{"use strict";let t=[];!function(){for(let e=0;e<256;e++){let r=e;for(let e=0;e<8;e++)1&r?r=0xedb88320^r>>>1:r>>>=1;t[e]=r}}();let r=e.exports=function(){this._crc=-1};r.prototype.write=function(e){for(let r=0;r<e.length;r++)this._crc=t[(this._crc^e[r])&255]^this._crc>>>8;return!0},r.prototype.crc32=function(){return -1^this._crc},r.crc32=function(e){let r=-1;for(let n=0;n<e.length;n++)r=t[(r^e[n])&255]^r>>>8;return -1^r}},6081:(e,t,r)=>{"use strict";let n=r(2412).ok,i=r(4075),o=r(8354),s=r(9428).kMaxLength;function a(e){if(!(this instanceof a))return new a(e);e&&e.chunkSize<i.Z_MIN_CHUNK&&(e.chunkSize=i.Z_MIN_CHUNK),i.Inflate.call(this,e),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,e&&null!=e.maxLength&&(this._maxLength=e.maxLength)}function l(e,t){t&&process.nextTick(t),e._handle&&(e._handle.close(),e._handle=null)}function u(e,t){var r=new a(t),n=e;if("string"==typeof n&&(n=Buffer.from(n)),!(n instanceof Buffer))throw TypeError("Not a string or buffer");let o=r._finishFlushFlag;return null==o&&(o=i.Z_FINISH),r._processChunk(n,o)}a.prototype._processChunk=function(e,t,r){let o,a;if("function"==typeof r)return i.Inflate._processChunk.call(this,e,t,r);let u=this,c=e&&e.length,f=this._chunkSize-this._offset,h=this._maxLength,d=0,p=[],g=0;this.on("error",function(e){o=e}),n(this._handle,"zlib binding closed");do a=(a=this._handle.writeSync(t,e,d,c,this._buffer,this._offset,f))||this._writeState;while(!this._hadError&&function(e,t){if(u._hadError)return;let r=f-t;if(n(r>=0,"have should not go down"),r>0){let e=u._buffer.slice(u._offset,u._offset+r);if(u._offset+=r,e.length>h&&(e=e.slice(0,h)),p.push(e),g+=e.length,0==(h-=e.length))return!1}return(0===t||u._offset>=u._chunkSize)&&(f=u._chunkSize,u._offset=0,u._buffer=Buffer.allocUnsafe(u._chunkSize)),0===t&&(d+=c-e,c=e,!0)}(a[0],a[1]));if(this._hadError)throw o;if(g>=s)throw l(this),RangeError("Cannot create final Buffer. It would be larger than 0x"+s.toString(16)+" bytes");let m=Buffer.concat(p,g);return l(this),m},o.inherits(a,i.Inflate),e.exports=t=u,t.Inflate=a,t.createInflate=function(e){return new a(e)},t.inflateSync=u},6155:(e,t)=>{t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let r={N1:3,N2:3,N3:40,N4:10};t.isValid=function(e){return null!=e&&""!==e&&!isNaN(e)&&e>=0&&e<=7},t.from=function(e){return t.isValid(e)?parseInt(e,10):void 0},t.getPenaltyN1=function(e){let t=e.size,n=0,i=0,o=0,s=null,a=null;for(let l=0;l<t;l++){i=o=0,s=a=null;for(let u=0;u<t;u++){let t=e.get(l,u);t===s?i++:(i>=5&&(n+=r.N1+(i-5)),s=t,i=1),(t=e.get(u,l))===a?o++:(o>=5&&(n+=r.N1+(o-5)),a=t,o=1)}i>=5&&(n+=r.N1+(i-5)),o>=5&&(n+=r.N1+(o-5))}return n},t.getPenaltyN2=function(e){let t=e.size,n=0;for(let r=0;r<t-1;r++)for(let i=0;i<t-1;i++){let t=e.get(r,i)+e.get(r,i+1)+e.get(r+1,i)+e.get(r+1,i+1);(4===t||0===t)&&n++}return n*r.N2},t.getPenaltyN3=function(e){let t=e.size,n=0,i=0,o=0;for(let r=0;r<t;r++){i=o=0;for(let s=0;s<t;s++)i=i<<1&2047|e.get(r,s),s>=10&&(1488===i||93===i)&&n++,o=o<<1&2047|e.get(s,r),s>=10&&(1488===o||93===o)&&n++}return n*r.N3},t.getPenaltyN4=function(e){let t=0,n=e.data.length;for(let r=0;r<n;r++)t+=e.data[r];return Math.abs(Math.ceil(100*t/n/5)-10)*r.N4},t.applyMask=function(e,r){let n=r.size;for(let i=0;i<n;i++)for(let o=0;o<n;o++)r.isReserved(o,i)||r.xor(o,i,function(e,r,n){switch(e){case t.Patterns.PATTERN000:return(r+n)%2==0;case t.Patterns.PATTERN001:return r%2==0;case t.Patterns.PATTERN010:return n%3==0;case t.Patterns.PATTERN011:return(r+n)%3==0;case t.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(n/3))%2==0;case t.Patterns.PATTERN101:return r*n%2+r*n%3==0;case t.Patterns.PATTERN110:return(r*n%2+r*n%3)%2==0;case t.Patterns.PATTERN111:return(r*n%3+(r+n)%2)%2==0;default:throw Error("bad maskPattern:"+e)}}(e,o,i))},t.getBestMask=function(e,r){let n=Object.keys(t.Patterns).length,i=0,o=1/0;for(let s=0;s<n;s++){r(s),t.applyMask(s,e);let n=t.getPenaltyN1(e)+t.getPenaltyN2(e)+t.getPenaltyN3(e)+t.getPenaltyN4(e);t.applyMask(s,e),n<o&&(o=n,i=s)}return i}},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return _},getUtils:function(){return m},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return g},normalizeVercelUrl:function(){return d}});let n=r(9551),i=r(1959),o=r(2437),s=r(4396),a=r(8034),l=r(5526),u=r(2887),c=r(4722),f=r(6143),h=r(7912);function d(e,t,r){let i=(0,n.parse)(e.url,!0);for(let e of(delete i.search,Object.keys(i.query))){let n=e!==f.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(f.NEXT_QUERY_PARAM_PREFIX),o=e!==f.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(f.NEXT_INTERCEPTION_MARKER_PREFIX);(n||o||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete i.query[e]}e.url=(0,n.format)(i)}function p(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let i,{optional:o,repeat:s}=r.groups[n],a=`[${s?"...":""}${n}]`;o&&(a=`[${a}]`);let l=t[n];i=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(a,i)}return e}function g(e,t,r,n){let i={};for(let o of Object.keys(t.groups)){let s=e[o];"string"==typeof s?s=(0,c.normalizeRscURL)(s):Array.isArray(s)&&(s=s.map(c.normalizeRscURL));let a=r[o],l=t.groups[o].optional;if((Array.isArray(a)?a.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(a))||void 0===s&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${o}]]`))&&(s=void 0,delete e[o]),s&&"string"==typeof s&&t.groups[o].repeat&&(s=s.split("/")),s&&(i[o]=s)}return{params:i,hasValidParams:!0}}function m({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:c,trailingSlash:f,caseSensitive:m}){let _,y,b;return c&&(_=(0,s.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(y=(0,a.getRouteMatcher)(_))(e)),{handleRewrites:function(s,a){let h={},d=a.pathname,p=n=>{let u=(0,o.getPathMatch)(n.source+(f?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!m});if(!a.pathname)return!1;let p=u(a.pathname);if((n.has||n.missing)&&p){let e=(0,l.matchHas)(s,a.query,n.has,n.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:o,destQuery:s}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:p,query:a.query});if(o.protocol)return!0;if(Object.assign(h,s,p),Object.assign(a.query,o.query),delete o.query,Object.assign(a,o),!(d=a.pathname))return!1;if(r&&(d=d.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(d,t.locales);d=e.pathname,a.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(d===e)return!0;if(c&&y){let e=y(d);if(e)return a.query={...a.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(d!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(d||"");return t===(0,u.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return h},defaultRouteRegex:_,dynamicRouteMatcher:y,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!_)return null;let{groups:t,routeKeys:r}=_,n=(0,a.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,h.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let i={};for(let e of Object.keys(r)){let o=r[e];if(!o)continue;let s=t[o],a=n[e];if(!s.optional&&!a)return null;i[s.pos]=a}return i}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>_&&b?g(e,_,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>d(e,t,_),interpolateDynamicPath:(e,t)=>p(e,t,_)}}function _(e,t){return"string"==typeof e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[f.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[f.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6343:(e,t,r)=>{let n=r(7458),i=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],o=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];t.getBlocksCount=function(e,t){switch(t){case n.L:return i[(e-1)*4+0];case n.M:return i[(e-1)*4+1];case n.Q:return i[(e-1)*4+2];case n.H:return i[(e-1)*4+3];default:return}},t.getTotalCodewordsCount=function(e,t){switch(t){case n.L:return o[(e-1)*4+0];case n.M:return o[(e-1)*4+1];case n.Q:return o[(e-1)*4+2];case n.H:return o[(e-1)*4+3];default:return}}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},o=t.split(n),s=(r||{}).decode||e,a=0;a<o.length;a++){var l=o[a],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),f=l.substr(++u,l.length).trim();'"'==f[0]&&(f=f.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(f,s))}}return i},t.serialize=function(e,t,n){var o=n||{},s=o.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var a=s(t);if(a&&!i.test(a))throw TypeError("argument val is invalid");var l=e+"="+a;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(o.domain){if(!i.test(o.domain))throw TypeError("option domain is invalid");l+="; Domain="+o.domain}if(o.path){if(!i.test(o.path))throw TypeError("option path is invalid");l+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(l+="; HttpOnly"),o.secure&&(l+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return E}});let n=r(4985),i=r(740),o=r(687),s=i._(r(3210)),a=n._(r(1215)),l=n._(r(512)),u=r(4953),c=r(2756),f=r(284);r(148);let h=r(9148),d=n._(r(1933)),p=r(3038),g={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function m(e,t,r,n,i,o,s){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function _(e){return s.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let y=(0,s.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:i,height:a,width:l,decoding:u,className:c,style:f,fetchPriority:h,placeholder:d,loading:g,unoptimized:y,fill:b,onLoadRef:E,onLoadingCompleteRef:x,setBlurComplete:v,setShowAltText:w,sizesInput:R,onLoad:P,onError:C,...T}=e,A=(0,s.useCallback)(e=>{e&&(C&&(e.src=e.src),e.complete&&m(e,d,E,x,v,y,R))},[r,d,E,x,v,C,y,R]),O=(0,p.useMergedRef)(t,A);return(0,o.jsx)("img",{...T,..._(h),loading:g,width:l,height:a,decoding:u,"data-nimg":b?"fill":"1",className:c,style:f,sizes:i,srcSet:n,src:r,ref:O,onLoad:e=>{m(e.currentTarget,d,E,x,v,y,R)},onError:e=>{w(!0),"empty"!==d&&v(!0),C&&C(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,..._(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,n),null):(0,o.jsx)(l.default,{children:(0,o.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let E=(0,s.forwardRef)((e,t)=>{let r=(0,s.useContext)(h.RouterContext),n=(0,s.useContext)(f.ImageConfigContext),i=(0,s.useMemo)(()=>{var e;let t=g||n||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),i=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:i,qualities:o}},[n]),{onLoad:a,onLoadingComplete:l}=e,p=(0,s.useRef)(a);(0,s.useEffect)(()=>{p.current=a},[a]);let m=(0,s.useRef)(l);(0,s.useEffect)(()=>{m.current=l},[l]);let[_,E]=(0,s.useState)(!1),[x,v]=(0,s.useState)(!1),{props:w,meta:R}=(0,u.getImgProps)(e,{defaultLoader:d.default,imgConf:i,blurComplete:_,showAltText:x});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(y,{...w,unoptimized:R.unoptimized,placeholder:R.placeholder,fill:R.fill,onLoadRef:p,onLoadingCompleteRef:m,setBlurComplete:E,setShowAltText:v,sizesInput:e.sizes,ref:t}),R.priority?(0,o.jsx)(b,{isAppRouter:!r,imgAttributes:w}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6701:(e,t,r)=>{let n=r(9483);function i(e,t){let r=e.a/255,n=t+'="'+e.hex+'"';return r<1?n+" "+t+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function o(e,t,r){let n=e+t;return void 0!==r&&(n+=" "+r),n}t.render=function(e,t,r){let s=n.getOptions(t),a=e.modules.size,l=e.modules.data,u=a+2*s.margin,c=s.color.light.a?"<path "+i(s.color.light,"fill")+' d="M0 0h'+u+"v"+u+'H0z"/>':"",f="<path "+i(s.color.dark,"stroke")+' d="'+function(e,t,r){let n="",i=0,s=!1,a=0;for(let l=0;l<e.length;l++){let u=Math.floor(l%t),c=Math.floor(l/t);u||s||(s=!0),e[l]?(a++,l>0&&u>0&&e[l-1]||(n+=s?o("M",u+r,.5+c+r):o("m",i,0),i=0,s=!1),u+1<t&&e[l+1]||(n+=o("h",a),a=0)):i++}return n}(l,a,s.margin)+'"/>',h='<svg xmlns="http://www.w3.org/2000/svg" '+(s.width?'width="'+s.width+'" height="'+s.width+'" ':"")+('viewBox="0 0 '+u+" ")+u+'" shape-rendering="crispEdges">'+c+f+"</svg>\n";return"function"==typeof r&&r(null,h),h}},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return o}});let n=r(2785),i=r(3736);function o(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6819:(e,t,r)=>{"use strict";let n=r(8354),i=r(7910),o=r(4967),s=r(8358),a=e.exports=function(e){i.call(this),this._packer=new s(e||{}),this._deflate=this._packer.createDeflate(),this.readable=!0};n.inherits(a,i),a.prototype.pack=function(e,t,r,n){this.emit("data",Buffer.from(o.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(t,r)),n&&this.emit("data",this._packer.packGAMA(n));let i=this._packer.filterData(e,t,r);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",(function(e){this.emit("data",this._packer.packIDAT(e))}).bind(this)),this._deflate.on("end",(function(){this.emit("data",this._packer.packIEND()),this.emit("end")}).bind(this)),this._deflate.end(i)}},7233:e=>{"use strict";var t={single_source_shortest_paths:function(e,r,n){var i,o,s,a,l,u,c,f={},h={};h[r]=0;var d=t.PriorityQueue.make();for(d.push(r,0);!d.empty();)for(s in o=(i=d.pop()).value,a=i.cost,l=e[o]||{})l.hasOwnProperty(s)&&(u=a+l[s],c=h[s],(void 0===h[s]||c>u)&&(h[s]=u,d.push(s,u),f[s]=o));if(void 0!==n&&void 0===h[n])throw Error(["Could not find a path from ",r," to ",n,"."].join(""));return f},extract_shortest_path_from_predecessor_list:function(e,t){for(var r=[],n=t;n;)r.push(n),e[n],n=e[n];return r.reverse(),r},find_path:function(e,r,n){var i=t.single_source_shortest_paths(e,r,n);return t.extract_shortest_path_from_predecessor_list(i,n)},PriorityQueue:{make:function(e){var r,n=t.PriorityQueue,i={};for(r in e=e||{},n)n.hasOwnProperty(r)&&(i[r]=n[r]);return i.queue=[],i.sorter=e.sorter||n.default_sorter,i},default_sorter:function(e,t){return e.cost-t.cost},push:function(e,t){this.queue.push({value:e,cost:t}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};e.exports=t},7425:(e,t,r)=>{"use strict";let n=r(8354),i=r(7910),o=r(9618),s=r(6819),a=r(8657),l=t.O=function(e){i.call(this),e=e||{},this.width=0|e.width,this.height=0|e.height,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,e.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new o(e),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",(function(e){this.data=e,this.emit("parsed",e)}).bind(this)),this._packer=new s(e),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};n.inherits(l,i),l.sync=a,l.prototype.pack=function(){return this.data&&this.data.length?process.nextTick((function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}).bind(this)):this.emit("error","No data provided"),this},l.prototype.parse=function(e,t){if(t){let e,r;e=(function(e){this.removeListener("error",r),this.data=e,t(null,this)}).bind(this),r=(function(r){this.removeListener("parsed",e),t(r,null)}).bind(this),this.once("parsed",e),this.once("error",r)}return this.end(e),this},l.prototype.write=function(e){return this._parser.write(e),!0},l.prototype.end=function(e){this._parser.end(e)},l.prototype._metadata=function(e){this.width=e.width,this.height=e.height,this.emit("metadata",e)},l.prototype._gamma=function(e){this.gamma=e},l.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},l.bitblt=function(e,t,r,n,i,o,s,a){if(n|=0,i|=0,o|=0,s|=0,a|=0,(r|=0)>e.width||n>e.height||r+i>e.width||n+o>e.height)throw Error("bitblt reading outside image");if(s>t.width||a>t.height||s+i>t.width||a+o>t.height)throw Error("bitblt writing outside image");for(let l=0;l<o;l++)e.data.copy(t.data,(a+l)*t.width+s<<2,(n+l)*e.width+r<<2,(n+l)*e.width+r+i<<2)},l.prototype.bitblt=function(e,t,r,n,i,o,s){return l.bitblt(this,e,t,r,n,i,o,s),this},l.adjustGamma=function(e){if(e.gamma){for(let t=0;t<e.height;t++)for(let r=0;r<e.width;r++){let n=e.width*t+r<<2;for(let t=0;t<3;t++){let r=e.data[n+t]/255;r=Math.pow(r,1/2.2/e.gamma),e.data[n+t]=Math.round(255*r)}}e.gamma=0}},l.prototype.adjustGamma=function(){l.adjustGamma(this)}},7458:(e,t)=>{t.L={bit:1},t.M={bit:0},t.Q={bit:3},t.H={bit:2},t.isValid=function(e){return e&&void 0!==e.bit&&e.bit>=0&&e.bit<4},t.from=function(e,r){if(t.isValid(e))return e;try{if("string"!=typeof e)throw Error("Param is not a string");switch(e.toLowerCase()){case"l":case"low":return t.L;case"m":case"medium":return t.M;case"q":case"quartile":return t.Q;case"h":case"high":return t.H;default:throw Error("Unknown EC Level: "+e)}}catch(e){return r}}},7755:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(3210),i=()=>{},o=()=>{};function s(e){var t;let{headManager:r,reduceComponentsToState:s}=e;function a(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(s(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),a(),i(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),i(()=>(r&&(r._pendingUpdate=a),()=>{r&&(r._pendingUpdate=a)})),o(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},7903:(e,t,r)=>{"use strict";let n=r(4967),i=r(5868),o=e.exports=function(e,t){this._options=e,e.checkCRC=!1!==e.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[n.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[n.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[n.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[n.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[n.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[n.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=t.read,this.error=t.error,this.metadata=t.metadata,this.gamma=t.gamma,this.transColor=t.transColor,this.palette=t.palette,this.parsed=t.parsed,this.inflateData=t.inflateData,this.finished=t.finished,this.simpleTransparency=t.simpleTransparency,this.headersFinished=t.headersFinished||function(){}};o.prototype.start=function(){this.read(n.PNG_SIGNATURE.length,this._parseSignature.bind(this))},o.prototype._parseSignature=function(e){let t=n.PNG_SIGNATURE;for(let r=0;r<t.length;r++)if(e[r]!==t[r])return void this.error(Error("Invalid file signature"));this.read(8,this._parseChunkBegin.bind(this))},o.prototype._parseChunkBegin=function(e){let t=e.readUInt32BE(0),r=e.readUInt32BE(4),o="";for(let t=4;t<8;t++)o+=String.fromCharCode(e[t]);let s=!!(32&e[4]);return this._hasIHDR||r===n.TYPE_IHDR?(this._crc=new i,this._crc.write(Buffer.from(o)),this._chunks[r])?this._chunks[r](t):s?void this.read(t+4,this._skipChunk.bind(this)):void this.error(Error("Unsupported critical chunk type "+o)):void this.error(Error("Expected IHDR on beggining"))},o.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},o.prototype._parseChunkEnd=function(e){let t=e.readInt32BE(0),r=this._crc.crc32();if(this._options.checkCRC&&r!==t)return void this.error(Error("Crc error - "+t+" - "+r));this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleIHDR=function(e){this.read(e,this._parseIHDR.bind(this))},o.prototype._parseIHDR=function(e){this._crc.write(e);let t=e.readUInt32BE(0),r=e.readUInt32BE(4),i=e[8],o=e[9],s=e[10],a=e[11],l=e[12];if(8!==i&&4!==i&&2!==i&&1!==i&&16!==i)return void this.error(Error("Unsupported bit depth "+i));if(!(o in n.COLORTYPE_TO_BPP_MAP))return void this.error(Error("Unsupported color type"));if(0!==s)return void this.error(Error("Unsupported compression method"));if(0!==a)return void this.error(Error("Unsupported filter method"));if(0!==l&&1!==l)return void this.error(Error("Unsupported interlace method"));this._colorType=o;let u=n.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:t,height:r,depth:i,interlace:!!l,palette:!!(o&n.COLORTYPE_PALETTE),color:!!(o&n.COLORTYPE_COLOR),alpha:!!(o&n.COLORTYPE_ALPHA),bpp:u,colorType:o}),this._handleChunkEnd()},o.prototype._handlePLTE=function(e){this.read(e,this._parsePLTE.bind(this))},o.prototype._parsePLTE=function(e){this._crc.write(e);let t=Math.floor(e.length/3);for(let r=0;r<t;r++)this._palette.push([e[3*r],e[3*r+1],e[3*r+2],255]);this.palette(this._palette),this._handleChunkEnd()},o.prototype._handleTRNS=function(e){this.simpleTransparency(),this.read(e,this._parseTRNS.bind(this))},o.prototype._parseTRNS=function(e){if(this._crc.write(e),this._colorType===n.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length)return void this.error(Error("Transparency chunk must be after palette"));if(e.length>this._palette.length)return void this.error(Error("More transparent colors than palette size"));for(let t=0;t<e.length;t++)this._palette[t][3]=e[t];this.palette(this._palette)}this._colorType===n.COLORTYPE_GRAYSCALE&&this.transColor([e.readUInt16BE(0)]),this._colorType===n.COLORTYPE_COLOR&&this.transColor([e.readUInt16BE(0),e.readUInt16BE(2),e.readUInt16BE(4)]),this._handleChunkEnd()},o.prototype._handleGAMA=function(e){this.read(e,this._parseGAMA.bind(this))},o.prototype._parseGAMA=function(e){this._crc.write(e),this.gamma(e.readUInt32BE(0)/n.GAMMA_DIVISION),this._handleChunkEnd()},o.prototype._handleIDAT=function(e){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-e,this._parseIDAT.bind(this,e))},o.prototype._parseIDAT=function(e,t){if(this._crc.write(t),this._colorType===n.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw Error("Expected palette not found");this.inflateData(t);let r=e-t.length;r>0?this._handleIDAT(r):this._handleChunkEnd()},o.prototype._handleIEND=function(e){this.read(e,this._parseIEND.bind(this))},o.prototype._parseIEND=function(e){this._crc.write(e),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}},7910:e=>{"use strict";e.exports=require("stream")},7920:(e,t,r)=>{let n=r(4902);function i(e){this.genPoly=void 0,this.degree=e,this.degree&&this.initialize(this.degree)}i.prototype.initialize=function(e){this.degree=e,this.genPoly=n.generateECPolynomial(this.degree)},i.prototype.encode=function(e){if(!this.genPoly)throw Error("Encoder not initialized");let t=new Uint8Array(e.length+this.degree);t.set(e);let r=n.mod(t,this.genPoly),i=this.degree-r.length;if(i>0){let e=new Uint8Array(this.degree);return e.set(r,i),e}return r},e.exports=i},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(4827);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(r)){let r=i[t.pos];void 0!==r&&(t.repeat?s[e]=r.split("/").map(e=>o(e)):s[e]=o(r))}return s}}},8082:(e,t,r)=>{let n=r(9227),i=r(7458),o=r(9096),s=r(9199),a=r(4868),l=r(2251),u=r(6155),c=r(6343),f=r(7920),h=r(9078),d=r(816),p=r(395),g=r(1414);function m(e,t,r){let n,i,o=e.size,s=d.getEncodedBits(t,r);for(n=0;n<15;n++)i=(s>>n&1)==1,n<6?e.set(n,8,i,!0):n<8?e.set(n+1,8,i,!0):e.set(o-15+n,8,i,!0),n<8?e.set(8,o-n-1,i,!0):n<9?e.set(8,15-n-1+1,i,!0):e.set(8,15-n-1,i,!0);e.set(o-8,8,1,!0)}t.create=function(e,t){let r,d;if(void 0===e||""===e)throw Error("No input text");let _=i.M;return void 0!==t&&(_=i.from(t.errorCorrectionLevel,i.M),r=h.from(t.version),d=u.from(t.maskPattern),t.toSJISFunc&&n.setToSJISFunction(t.toSJISFunc)),function(e,t,r,i){let d;if(Array.isArray(e))d=g.fromArray(e);else if("string"==typeof e){let n=t;if(!n){let t=g.rawSplit(e);n=h.getBestVersionForData(t,r)}d=g.fromString(e,n||40)}else throw Error("Invalid data");let _=h.getBestVersionForData(d,r);if(!_)throw Error("The amount of data is too big to be stored in a QR Code");if(t){if(t<_)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+_+".\n")}else t=_;let y=function(e,t,r){let i=new o;r.forEach(function(t){i.put(t.mode.bit,4),i.put(t.getLength(),p.getCharCountIndicator(t.mode,e)),t.write(i)});let s=(n.getSymbolTotalCodewords(e)-c.getTotalCodewordsCount(e,t))*8;for(i.getLengthInBits()+4<=s&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(0);let a=(s-i.getLengthInBits())/8;for(let e=0;e<a;e++)i.put(e%2?17:236,8);return function(e,t,r){let i,o,s=n.getSymbolTotalCodewords(t),a=s-c.getTotalCodewordsCount(t,r),l=c.getBlocksCount(t,r),u=s%l,h=l-u,d=Math.floor(s/l),p=Math.floor(a/l),g=p+1,m=d-p,_=new f(m),y=0,b=Array(l),E=Array(l),x=0,v=new Uint8Array(e.buffer);for(let e=0;e<l;e++){let t=e<h?p:g;b[e]=v.slice(y,y+t),E[e]=_.encode(b[e]),y+=t,x=Math.max(x,t)}let w=new Uint8Array(s),R=0;for(i=0;i<x;i++)for(o=0;o<l;o++)i<b[o].length&&(w[R++]=b[o][i]);for(i=0;i<m;i++)for(o=0;o<l;o++)w[R++]=E[o][i];return w}(i,e,t)}(t,r,d),b=new s(n.getSymbolSize(t));!function(e,t){let r=e.size,n=l.getPositions(t);for(let t=0;t<n.length;t++){let i=n[t][0],o=n[t][1];for(let t=-1;t<=7;t++)if(!(i+t<=-1)&&!(r<=i+t))for(let n=-1;n<=7;n++)o+n<=-1||r<=o+n||(t>=0&&t<=6&&(0===n||6===n)||n>=0&&n<=6&&(0===t||6===t)||t>=2&&t<=4&&n>=2&&n<=4?e.set(i+t,o+n,!0,!0):e.set(i+t,o+n,!1,!0))}}(b,t);let E=b.size;for(let e=8;e<E-8;e++){let t=e%2==0;b.set(e,6,t,!0),b.set(6,e,t,!0)}return!function(e,t){let r=a.getPositions(t);for(let t=0;t<r.length;t++){let n=r[t][0],i=r[t][1];for(let t=-2;t<=2;t++)for(let r=-2;r<=2;r++)-2===t||2===t||-2===r||2===r||0===t&&0===r?e.set(n+t,i+r,!0,!0):e.set(n+t,i+r,!1,!0)}}(b,t),m(b,r,0),t>=7&&function(e,t){let r,n,i,o=e.size,s=h.getEncodedBits(t);for(let t=0;t<18;t++)r=Math.floor(t/3),n=t%3+o-8-3,i=(s>>t&1)==1,e.set(r,n,i,!0),e.set(n,r,i,!0)}(b,t),!function(e,t){let r=e.size,n=-1,i=r-1,o=7,s=0;for(let a=r-1;a>0;a-=2)for(6===a&&a--;;){for(let r=0;r<2;r++)if(!e.isReserved(i,a-r)){let n=!1;s<t.length&&(n=(t[s]>>>o&1)==1),e.set(i,a-r,n),-1==--o&&(s++,o=7)}if((i+=n)<0||r<=i){i-=n,n=-n;break}}}(b,y),isNaN(i)&&(i=u.getBestMask(b,m.bind(null,b,r))),u.applyMask(i,b),m(b,r,i),{modules:b,version:t,errorCorrectionLevel:r,maskPattern:i,segments:d}}(e,r,_,d)}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8260:(e,t,r)=>{"use strict";let n=r(5299),i=[function(){},function(e,t,r,n){if(n===t.length)throw Error("Ran out of data");let i=t[n];e[r]=i,e[r+1]=i,e[r+2]=i,e[r+3]=255},function(e,t,r,n){if(n+1>=t.length)throw Error("Ran out of data");let i=t[n];e[r]=i,e[r+1]=i,e[r+2]=i,e[r+3]=t[n+1]},function(e,t,r,n){if(n+2>=t.length)throw Error("Ran out of data");e[r]=t[n],e[r+1]=t[n+1],e[r+2]=t[n+2],e[r+3]=255},function(e,t,r,n){if(n+3>=t.length)throw Error("Ran out of data");e[r]=t[n],e[r+1]=t[n+1],e[r+2]=t[n+2],e[r+3]=t[n+3]}],o=[function(){},function(e,t,r,n){let i=t[0];e[r]=i,e[r+1]=i,e[r+2]=i,e[r+3]=n},function(e,t,r){let n=t[0];e[r]=n,e[r+1]=n,e[r+2]=n,e[r+3]=t[1]},function(e,t,r,n){e[r]=t[0],e[r+1]=t[1],e[r+2]=t[2],e[r+3]=n},function(e,t,r){e[r]=t[0],e[r+1]=t[1],e[r+2]=t[2],e[r+3]=t[3]}];t.dataToBitMap=function(e,t){let r,s,a,l,u,c,f=t.width,h=t.height,d=t.depth,p=t.bpp,g=t.interlace;8!==d&&(r=[],s=0,a={get:function(t){for(;r.length<t;)!function(){let t,n,i,o;if(s===e.length)throw Error("Ran out of data");let a=e[s];switch(s++,d){default:throw Error("unrecognised depth");case 16:i=e[s],s++,r.push((a<<8)+i);break;case 4:i=15&a,o=a>>4,r.push(o,i);break;case 2:t=3&a,n=a>>2&3,i=a>>4&3,o=a>>6&3,r.push(o,i,n,t);break;case 1:t=a>>4&1,n=a>>5&1,i=a>>6&1,o=a>>7&1,r.push(o,i,n,t,a>>3&1,a>>2&1,a>>1&1,1&a)}}();let n=r.slice(0,t);return r=r.slice(t),n},resetAfterLine:function(){r.length=0},end:function(){if(s!==e.length)throw Error("extra data found")}}),l=d<=8?Buffer.alloc(f*h*4):new Uint16Array(f*h*4);let m=Math.pow(2,d)-1,_=0;if(g)u=n.getImagePasses(f,h),c=n.getInterlaceIterator(f,h);else{let e=0;c=function(){let t=e;return e+=4,t},u=[{width:f,height:h}]}for(let t=0;t<u.length;t++)8===d?_=function(e,t,r,n,o,s){let a=e.width,l=e.height,u=e.index;for(let e=0;e<l;e++)for(let l=0;l<a;l++){let a=r(l,e,u);i[n](t,o,a,s),s+=n}return s}(u[t],l,c,p,e,_):function(e,t,r,n,i,s){let a=e.width,l=e.height,u=e.index;for(let e=0;e<l;e++){for(let l=0;l<a;l++){let a=i.get(n),c=r(l,e,u);o[n](t,a,c,s)}i.resetAfterLine()}}(u[t],l,c,p,a,m);if(8===d){if(_!==e.length)throw Error("extra data found")}else a.end();return l}},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return a},STATIC_METADATA_IMAGES:function(){return s},getExtensionRegexString:function(){return l},isMetadataPage:function(){return f},isMetadataRoute:function(){return h},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let n=r(2958),i=r(4722),o=r(554),s={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},a=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,r){let i=(r?"":"?")+"$",o=`\\d?${r?"":"(-\\w{6})?"}`,a=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${i}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${i}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${i}`),RegExp(`[\\\\/]${s.icon.filename}${o}${l(s.icon.extensions,t)}${i}`),RegExp(`[\\\\/]${s.apple.filename}${o}${l(s.apple.extensions,t)}${i}`),RegExp(`[\\\\/]${s.openGraph.filename}${o}${l(s.openGraph.extensions,t)}${i}`),RegExp(`[\\\\/]${s.twitter.filename}${o}${l(s.twitter.extensions,t)}${i}`)],u=(0,n.normalizePathSep)(e);return a.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,o.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function f(e){return!(0,o.isAppRouteRoute)(e)&&u(e,[],!1)}function h(e){let t=(0,i.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,o.isAppRouteRoute)(e)&&u(t,[],!1)}},8354:e=>{"use strict";e.exports=require("util")},8358:(e,t,r)=>{"use strict";let n=r(4967),i=r(5868),o=r(431),s=r(2214),a=r(4075),l=e.exports=function(e){if(this._options=e,e.deflateChunkSize=e.deflateChunkSize||32768,e.deflateLevel=null!=e.deflateLevel?e.deflateLevel:9,e.deflateStrategy=null!=e.deflateStrategy?e.deflateStrategy:3,e.inputHasAlpha=null==e.inputHasAlpha||e.inputHasAlpha,e.deflateFactory=e.deflateFactory||a.createDeflate,e.bitDepth=e.bitDepth||8,e.colorType="number"==typeof e.colorType?e.colorType:n.COLORTYPE_COLOR_ALPHA,e.inputColorType="number"==typeof e.inputColorType?e.inputColorType:n.COLORTYPE_COLOR_ALPHA,-1===[n.COLORTYPE_GRAYSCALE,n.COLORTYPE_COLOR,n.COLORTYPE_COLOR_ALPHA,n.COLORTYPE_ALPHA].indexOf(e.colorType))throw Error("option color type:"+e.colorType+" is not supported at present");if(-1===[n.COLORTYPE_GRAYSCALE,n.COLORTYPE_COLOR,n.COLORTYPE_COLOR_ALPHA,n.COLORTYPE_ALPHA].indexOf(e.inputColorType))throw Error("option input color type:"+e.inputColorType+" is not supported at present");if(8!==e.bitDepth&&16!==e.bitDepth)throw Error("option bit depth:"+e.bitDepth+" is not supported at present")};l.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},l.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},l.prototype.filterData=function(e,t,r){let i=o(e,t,r,this._options),a=n.COLORTYPE_TO_BPP_MAP[this._options.colorType];return s(i,t,r,this._options,a)},l.prototype._packChunk=function(e,t){let r=t?t.length:0,n=Buffer.alloc(r+12);return n.writeUInt32BE(r,0),n.writeUInt32BE(e,4),t&&t.copy(n,8),n.writeInt32BE(i.crc32(n.slice(4,n.length-4)),n.length-4),n},l.prototype.packGAMA=function(e){let t=Buffer.alloc(4);return t.writeUInt32BE(Math.floor(e*n.GAMMA_DIVISION),0),this._packChunk(n.TYPE_gAMA,t)},l.prototype.packIHDR=function(e,t){let r=Buffer.alloc(13);return r.writeUInt32BE(e,0),r.writeUInt32BE(t,4),r[8]=this._options.bitDepth,r[9]=this._options.colorType,r[10]=0,r[11]=0,r[12]=0,this._packChunk(n.TYPE_IHDR,r)},l.prototype.packIDAT=function(e){return this._packChunk(n.TYPE_IDAT,e)},l.prototype.packIEND=function(){return this._packChunk(n.TYPE_IEND,null)}},8653:(e,t,r)=>{let n=r(9021),i=r(7425).O,o=r(9483);t.render=function(e,t){let r=o.getOptions(t),n=r.rendererOpts,s=o.getImageWidth(e.modules.size,r);n.width=s,n.height=s;let a=new i(n);return o.qrToImageData(a.data,e,r),a},t.renderToDataURL=function(e,r,n){void 0===n&&(n=r,r=void 0),t.renderToBuffer(e,r,function(e,t){e&&n(e);let r="data:image/png;base64,";r+=t.toString("base64"),n(null,r)})},t.renderToBuffer=function(e,r,n){void 0===n&&(n=r,r=void 0);let i=t.render(e,r),o=[];i.on("error",n),i.on("data",function(e){o.push(e)}),i.on("end",function(){n(null,Buffer.concat(o))}),i.pack()},t.renderToFile=function(e,r,i,o){void 0===o&&(o=i,i=void 0);let s=!1,a=(...e)=>{s||(s=!0,o.apply(null,e))},l=n.createWriteStream(e);l.on("error",a),l.on("close",a),t.renderToFileStream(l,r,i)},t.renderToFileStream=function(e,r,n){t.render(r,n).pack().pipe(e)}},8657:(e,t,r)=>{"use strict";let n=r(8940),i=r(1220);t.read=function(e,t){return n(e,t||{})},t.write=function(e,t){return i(e,t)}},8808:(e,t,r)=>{t.render=r(6701).render,t.renderToFile=function(e,n,i,o){void 0===o&&(o=i,i=void 0);let s=r(9021),a=t.render(n,i);s.writeFile(e,'<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+a,o)}},8881:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},8940:(e,t,r)=>{"use strict";let n=!0,i=r(4075),o=r(6081);i.deflateSync||(n=!1);let s=r(2511),a=r(4581),l=r(7903),u=r(8260),c=r(2e3);e.exports=function(e,t){let r,f,h,d;if(!n)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let p=[],g=new s(e);if(new l(t,{read:g.read.bind(g),error:function(e){r=e},metadata:function(e){f=e},gamma:function(e){h=e},palette:function(e){f.palette=e},transColor:function(e){f.transColor=e},inflateData:function(e){p.push(e)},simpleTransparency:function(){f.alpha=!0}}).start(),g.process(),r)throw r;let m=Buffer.concat(p);if(p.length=0,f.interlace)d=i.inflateSync(m);else{let e=((f.width*f.bpp*f.depth+7>>3)+1)*f.height;d=o(m,{chunkSize:e,maxLength:e})}if(m=null,!d||!d.length)throw Error("bad png - invalid inflate data response");let _=a.process(d,f);m=null;let y=u.dataToBitMap(_,f);_=null;let b=c(y,f);return f.data=b,f.gamma=h||0,f}},9021:e=>{"use strict";e.exports=require("fs")},9078:(e,t,r)=>{let n=r(9227),i=r(6343),o=r(7458),s=r(395),a=r(3751),l=n.getBCHDigit(7973);function u(e,t){return s.getCharCountIndicator(e,t)+4}t.from=function(e,t){return a.isValid(e)?parseInt(e,10):t},t.getCapacity=function(e,t,r){if(!a.isValid(e))throw Error("Invalid QR Code version");void 0===r&&(r=s.BYTE);let o=(n.getSymbolTotalCodewords(e)-i.getTotalCodewordsCount(e,t))*8;if(r===s.MIXED)return o;let l=o-u(r,e);switch(r){case s.NUMERIC:return Math.floor(l/10*3);case s.ALPHANUMERIC:return Math.floor(l/11*2);case s.KANJI:return Math.floor(l/13);case s.BYTE:default:return Math.floor(l/8)}},t.getBestVersionForData=function(e,r){let n,i=o.from(r,o.M);if(Array.isArray(e)){if(e.length>1){for(let r=1;r<=40;r++)if(function(e,t){let r=0;return e.forEach(function(e){let n=u(e.mode,t);r+=n+e.getBitsLength()}),r}(e,r)<=t.getCapacity(r,i,s.MIXED))return r;return}if(0===e.length)return 1;n=e[0]}else n=e;return function(e,r,n){for(let i=1;i<=40;i++)if(r<=t.getCapacity(i,n,e))return i}(n.mode,n.getLength(),i)},t.getEncodedBits=function(e){if(!a.isValid(e)||e<7)throw Error("Invalid QR Code version");let t=e<<12;for(;n.getBCHDigit(t)-l>=0;)t^=7973<<n.getBCHDigit(t)-l;return e<<12|t}},9090:(e,t,r)=>{let n=r(395);function i(e){this.mode=n.NUMERIC,this.data=e.toString()}i.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(e){let t,r,n;for(t=0;t+3<=this.data.length;t+=3)n=parseInt(this.data.substr(t,3),10),e.put(n,10);let i=this.data.length-t;i>0&&(n=parseInt(this.data.substr(t),10),e.put(n,3*i+1))},e.exports=i},9096:e=>{function t(){this.buffer=[],this.length=0}t.prototype={get:function(e){let t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)==1},put:function(e,t){for(let r=0;r<t;r++)this.putBit((e>>>t-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(e){let t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}},e.exports=t},9108:(e,t,r)=>{"use strict";let n=r(5299),i=r(2213);function o(e,t,r){let n=e*t;return 8!==r&&(n=Math.ceil(n/(8/r))),n}let s=e.exports=function(e,t){let r=e.width,i=e.height,s=e.interlace,a=e.bpp,l=e.depth;if(this.read=t.read,this.write=t.write,this.complete=t.complete,this._imageIndex=0,this._images=[],s){let e=n.getImagePasses(r,i);for(let t=0;t<e.length;t++)this._images.push({byteWidth:o(e[t].width,a,l),height:e[t].height,lineIndex:0})}else this._images.push({byteWidth:o(r,a,l),height:i,lineIndex:0});8===l?this._xComparison=a:16===l?this._xComparison=2*a:this._xComparison=1};s.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},s.prototype._unFilterType1=function(e,t,r){let n=this._xComparison,i=n-1;for(let o=0;o<r;o++){let r=e[1+o],s=o>i?t[o-n]:0;t[o]=r+s}},s.prototype._unFilterType2=function(e,t,r){let n=this._lastLine;for(let i=0;i<r;i++){let r=e[1+i],o=n?n[i]:0;t[i]=r+o}},s.prototype._unFilterType3=function(e,t,r){let n=this._xComparison,i=n-1,o=this._lastLine;for(let s=0;s<r;s++){let r=e[1+s],a=o?o[s]:0,l=Math.floor(((s>i?t[s-n]:0)+a)/2);t[s]=r+l}},s.prototype._unFilterType4=function(e,t,r){let n=this._xComparison,o=n-1,s=this._lastLine;for(let a=0;a<r;a++){let r=e[1+a],l=s?s[a]:0,u=i(a>o?t[a-n]:0,l,a>o&&s?s[a-n]:0);t[a]=r+u}},s.prototype._reverseFilterLine=function(e){let t,r=e[0],n=this._images[this._imageIndex],i=n.byteWidth;if(0===r)t=e.slice(1,i+1);else switch(t=Buffer.alloc(i),r){case 1:this._unFilterType1(e,t,i);break;case 2:this._unFilterType2(e,t,i);break;case 3:this._unFilterType3(e,t,i);break;case 4:this._unFilterType4(e,t,i);break;default:throw Error("Unrecognised filter type - "+r)}this.write(t),n.lineIndex++,n.lineIndex>=n.height?(this._lastLine=null,this._imageIndex++,n=this._images[this._imageIndex]):this._lastLine=t,n?this.read(n.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9148:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.RouterContext},9199:e=>{function t(e){if(!e||e<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=e,this.data=new Uint8Array(e*e),this.reservedBit=new Uint8Array(e*e)}t.prototype.set=function(e,t,r,n){let i=e*this.size+t;this.data[i]=r,n&&(this.reservedBit[i]=!0)},t.prototype.get=function(e,t){return this.data[e*this.size+t]},t.prototype.xor=function(e,t,r){this.data[e*this.size+t]^=r},t.prototype.isReserved=function(e,t){return this.reservedBit[e*this.size+t]},e.exports=t},9227:(e,t)=>{let r,n=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];t.getSymbolSize=function(e){if(!e)throw Error('"version" cannot be null or undefined');if(e<1||e>40)throw Error('"version" should be in range from 1 to 40');return 4*e+17},t.getSymbolTotalCodewords=function(e){return n[e]},t.getBCHDigit=function(e){let t=0;for(;0!==e;)t++,e>>>=1;return t},t.setToSJISFunction=function(e){if("function"!=typeof e)throw Error('"toSJISFunc" is not a valid function.');r=e},t.isKanjiModeEnabled=function(){return void 0!==r},t.toSJIS=function(e){return r(e)}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9483:(e,t)=>{function r(e){if("number"==typeof e&&(e=e.toString()),"string"!=typeof e)throw Error("Color should be defined as hex string");let t=e.slice().replace("#","").split("");if(t.length<3||5===t.length||t.length>8)throw Error("Invalid hex color: "+e);(3===t.length||4===t.length)&&(t=Array.prototype.concat.apply([],t.map(function(e){return[e,e]}))),6===t.length&&t.push("F","F");let r=parseInt(t.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+t.slice(0,6).join("")}}t.getOptions=function(e){e||(e={}),e.color||(e.color={});let t=void 0===e.margin||null===e.margin||e.margin<0?4:e.margin,n=e.width&&e.width>=21?e.width:void 0,i=e.scale||4;return{width:n,scale:n?4:i,margin:t,color:{dark:r(e.color.dark||"#000000ff"),light:r(e.color.light||"#ffffffff")},type:e.type,rendererOpts:e.rendererOpts||{}}},t.getScale=function(e,t){return t.width&&t.width>=e+2*t.margin?t.width/(e+2*t.margin):t.scale},t.getImageWidth=function(e,r){let n=t.getScale(e,r);return Math.floor((e+2*r.margin)*n)},t.qrToImageData=function(e,r,n){let i=r.modules.size,o=r.modules.data,s=t.getScale(i,n),a=Math.floor((i+2*n.margin)*s),l=n.margin*s,u=[n.color.light,n.color.dark];for(let t=0;t<a;t++)for(let r=0;r<a;r++){let c=(t*a+r)*4,f=n.color.light;t>=l&&r>=l&&t<a-l&&r<a-l&&(f=u[+!!o[Math.floor((t-l)/s)*i+Math.floor((r-l)/s)]]),e[c++]=f.r,e[c++]=f.g,e[c++]=f.b,e[c]=f.a}}},9513:(e,t,r)=>{"use strict";e.exports=r(4041).vendored.contexts.HeadManagerContext},9551:e=>{"use strict";e.exports=require("url")},9597:(e,t,r)=>{"use strict";let n=r(8354),i=r(539),o=r(9108),s=e.exports=function(e){i.call(this);let t=[],r=this;this._filter=new o(e,{read:this.read.bind(this),write:function(e){t.push(e)},complete:function(){r.emit("complete",Buffer.concat(t))}}),this._filter.start()};n.inherits(s,i)},9618:(e,t,r)=>{"use strict";let n=r(8354),i=r(4075),o=r(539),s=r(9597),a=r(7903),l=r(8260),u=r(2e3),c=e.exports=function(e){o.call(this),this._parser=new a(e,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=e,this.writable=!0,this._parser.start()};n.inherits(c,o),c.prototype._handleError=function(e){this.emit("error",e),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0},c.prototype._inflateData=function(e){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=i.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let e=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,t=Math.max(e,i.Z_MIN_CHUNK);this._inflate=i.createInflate({chunkSize:t});let r=e,n=this.emit.bind(this,"error");this._inflate.on("error",function(e){r&&n(e)}),this._filter.on("complete",this._complete.bind(this));let o=this._filter.write.bind(this._filter);this._inflate.on("data",function(e){r&&(e.length>r&&(e=e.slice(0,r)),r-=e.length,o(e))}),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(e)},c.prototype._handleMetaData=function(e){this._metaData=e,this._bitmapInfo=Object.create(e),this._filter=new s(this._bitmapInfo)},c.prototype._handleTransColor=function(e){this._bitmapInfo.transColor=e},c.prototype._handlePalette=function(e){this._bitmapInfo.palette=e},c.prototype._simpleTransparency=function(){this._metaData.alpha=!0},c.prototype._headersFinished=function(){this.emit("metadata",this._metaData)},c.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))},c.prototype._complete=function(e){let t;if(!this.errord){try{let r=l.dataToBitMap(e,this._bitmapInfo);t=u(r,this._bitmapInfo),r=null}catch(e){this._handleError(e);return}this.emit("parsed",t)}}},9642:e=>{e.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},9815:(e,t)=>{t.render=function(e,t,r){let n=e.modules.size,i=e.modules.data,o="\x1b[47m  \x1b[0m",s="",a=Array(n+3).join(o),l=[,,].join(o);s+=a+"\n";for(let e=0;e<n;++e){s+=o;for(let t=0;t<n;t++)s+=i[e*n+t]?"\x1b[40m  \x1b[0m":o;s+=l+"\n"}return s+=a+"\n","function"==typeof r&&r(null,s),s}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,559],()=>r(1667));module.exports=n})();